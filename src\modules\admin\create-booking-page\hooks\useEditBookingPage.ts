import type { CreateBookingPagePayload } from '../../apis/booking-page.api'
import { useRouter } from '@/libs/i18nNavigation'
import { useState } from 'react'
import { toast } from 'sonner'
import { bookingPageAPIs } from '../../apis/booking-page.api'
import { useBookingPageConfigStore } from '../stores/booking-page-config'

export type ValidationResult = {
  isValid: boolean
  errorType?: 'template' | 'blocks' | 'pageInfo'
}

export const useEditBookingPage = () => {
  const [isSubmiting, setIsSubmiting] = useState(false)
  const router = useRouter()

  // Validate booking page configuration
  const validateBookingPage = (): ValidationResult => {
    const templateSelected = useBookingPageConfigStore.getState().templateSelected
    // const blocks = useBookingPageConfigStore.getState().blocks

    // Validate that a template is selected
    if (!templateSelected?.id) {
      toast.error('Vui lòng chọn một template trước khi tạo page')
      return { isValid: false, errorType: 'template' }
    }

    // Validate that the booking page has at least one booking form block
    // const hasBookingForm = blocks.some(block => block.type === 'booking_ticket')
    // if (!hasBookingForm) {
    //   toast.error('Trang booking cần có ít nhất một khối form đặt chỗ')
    //   return { isValid: false, errorType: 'blocks' }
    // }

    const pageInfo = useBookingPageConfigStore.getState().pageInfo

    // Validate page information
    if (!pageInfo.name || !pageInfo.slug) {
      toast.error(
        !pageInfo.name
          ? 'Vui lòng nhập tên cho trang booking'
          : 'Vui lòng nhập đường dẫn truy cập cho trang booking',
      )
      return { isValid: false, errorType: 'pageInfo' }
    }

    return { isValid: true }
  }

  // Function to create the booking page
  const updateBookingPage = async () => {
    const defaultData = useBookingPageConfigStore.getState().defaultData

    try {
      if (!defaultData?._id) {
        toast.error('Đã xảy ra lỗi khi tạo booking page', {
          description: 'Vui lòng thử lại sau hoặc liên hệ quản trị viên.',
        })
        return
      }

      const pageInfo = useBookingPageConfigStore.getState().pageInfo
      const themeSettings = useBookingPageConfigStore.getState().themeSettings
      const templateSelected = useBookingPageConfigStore.getState().templateSelected
      const blocks = useBookingPageConfigStore.getState().blocks

      // In a real application, this would be an API call to save the booking page
      // For now, we'll simulate a successful creation
      // Create a new booking page object with the current configuration
      const newBookingPage: CreateBookingPagePayload = {
        name: pageInfo.name,
        description: pageInfo.description,
        slug: pageInfo.slug,
        templateCode: templateSelected.templateCode,
        blocks,
        theme: themeSettings,
      }

      const response = await bookingPageAPIs.updateBookingPage(defaultData?._id, newBookingPage)

      if (response?.status?.success) {
        // Show success message
        toast.success('Câp nhật booking page thành công!', {
          description: 'Bạn sẽ được chuyển đến trang quản lý booking pages.',
        })

        // Redirect to booking pages management screen
        router.push('/admin/manage-booking-pages')
      } else {
        toast.error('Đã xảy ra lỗi khi tạo booking page', {
          description: response?.status?.message || 'Vui lòng thử lại sau hoặc liên hệ quản trị viên.',
        })
      }
    } catch (error) {
      console.error('Error creating booking page:', error)
      toast.error('Đã xảy ra lỗi khi tạo booking page', {
        description: 'Vui lòng thử lại sau hoặc liên hệ quản trị viên.',
      })
    } finally {
      // If we don't redirect (due to error), make sure to reset the creating state
      // This will be executed if there's an error in the try block
      // In the success case, we redirect so this doesn't matter
      setIsSubmiting(false)
    }
  }

  // Handle the create page button click
  const handleSubmitPage = () => {
    // Set creating state and show confirmation toast
    setIsSubmiting(true)

    // Use toast to confirm and start the creation process
    toast.promise(
      new Promise((resolve) => {
        // Simulate API call delay
        updateBookingPage().then(resolve)
      }),
      {
        loading: 'Đang chuẩn bị tạo booking page...',
        success: async () => {
          return 'Đã xử lý xong!'
        },
        error: 'Đã xảy ra lỗi khi tạo booking page',
      },
    )
  }

  return {
    isSubmiting,
    handleSubmitPage,
    validateBookingPage,
  }
}
