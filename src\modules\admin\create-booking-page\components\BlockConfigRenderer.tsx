import type { Block } from '../types/blocks'
// Import UI components
import { <PERSON><PERSON> } from '@/components/ui/button'

import React from 'react'
import BlockActions from './BlockActions'
// Import block config components
import { AvailabilityCalendarBlockConfig } from './blocks/AvailabilityCalendarBlockConfig'

import { BannerBlockConfig } from './blocks/BannerBlock'
import { BookingTicketBlockConfig } from './blocks/BookingTicketBlock'
import { DescriptionBlockConfig } from './blocks/DescriptionBlock'
import { InfoBlockConfig } from './blocks/InfoBlock'
import { MapBlockConfig } from './blocks/MapBlock'
import CollapsibleBlock from './CollapsibleBlock'

interface BlockConfigRendererProps {
  blocks: Block[]
  onBlockChange: (index: number, updatedBlock: Block) => void
  onReorderBlocks?: (sourceIndex: number, destinationIndex: number) => void
  onAddBlock?: (blockType: Block['type']) => void
  onRemoveBlock?: (index: number) => void
}

/**
 * BlockConfigRenderer Component
 *
 * Renders configuration forms for a list of blocks
 */
export const BlockConfigRenderer: React.FC<BlockConfigRendererProps> = ({
  blocks,
  onBlockChange,
  onReorderBlocks,
  onAddBlock,
  onRemoveBlock,
}) => {
  // Handle move up action
  const handleMoveUp = (index: number) => {
    if (onReorderBlocks && index > 0) {
      onReorderBlocks(index, index - 1)
    }
  }

  // Handle move down action
  const handleMoveDown = (index: number) => {
    if (onReorderBlocks && index < blocks.length - 1) {
      onReorderBlocks(index, index + 1)
    }
  }

  return (
    <div className="space-y-8">
      {blocks.map((block, index) => (
        <CollapsibleBlock
          key={`block-${block.type}-${block.data.id || index}`}
          title={`${getBlockTitle(block.type)} Block`}
          defaultOpen={true}
          actions={
            (onReorderBlocks || onRemoveBlock) && (
              <BlockActions
                index={index}
                totalBlocks={blocks.length}
                onMoveUp={onReorderBlocks ? handleMoveUp : undefined}
                onMoveDown={onReorderBlocks ? handleMoveDown : undefined}
                onRemove={onRemoveBlock}
              />
            )
          }
        >

          <SingleBlockConfigRenderer
            block={block}
            onChange={(updatedData) => {
              const updatedBlock = { ...block, data: updatedData }
              onBlockChange(index, updatedBlock)
            }}
          />
        </CollapsibleBlock>
      ))}

      {onAddBlock && (
        <CollapsibleBlock
          key="add-new-block"
          title="Add New Block"
          defaultOpen={true}
          actions={null}
        >
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {['banner', 'info', 'description', 'map', 'availability_calendar', 'booking_ticket'].map(blockType => (
              <Button
                key={blockType}
                type="button"
                variant="outline"
                onClick={() => onAddBlock(blockType as Block['type'])}
                className="p-2 h-auto"
              >
                {getBlockTitle(blockType as Block['type'])}
              </Button>
            ))}
          </div>
        </CollapsibleBlock>
      )}
    </div>
  )
}

// Helper function to get a human-readable title for each block type
const getBlockTitle = (type: Block['type']): string => {
  switch (type) {
    case 'banner': return 'Banner'
    case 'info': return 'Information'
    case 'description': return 'Description'
    case 'map': return 'Map'
    case 'availability_calendar': return 'Availability Calendar'
    case 'booking_ticket': return 'Booking Ticket'
    default: return 'Block'
  }
}

// Component to render a single block's configuration form based on its type
const SingleBlockConfigRenderer: React.FC<{
  block: Block
  onChange: (updatedData: Record<string, any>) => void
}> = ({ block, onChange }) => {
  switch (block.type) {
    case 'banner':
      return <BannerBlockConfig data={block.data as any} onChange={onChange} />

    case 'info':
      return <InfoBlockConfig data={block.data as any} onChange={onChange} />

    case 'description':
      return <DescriptionBlockConfig data={block.data as any} onChange={onChange} />

      // case 'booking_form':
      //   return <BookingTicketBlockConfig data={block.data as any} onChange={onChange} />
      //   // return <BookingFormBlockConfig data={block.data as any} onChange={onChange} />

    case 'map':
      return <MapBlockConfig data={block.data as any} onChange={onChange} />

    case 'availability_calendar':
      return <AvailabilityCalendarBlockConfig data={block.data as any} onChange={onChange} />

    case 'booking_ticket':
      return <BookingTicketBlockConfig data={block.data as any} onChange={onChange} />

    default:
      return <div>Unknown block type</div>
  }
}
