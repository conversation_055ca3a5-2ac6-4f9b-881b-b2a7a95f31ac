import type { NextRequest, NextResponse } from 'next/server'
import type { LocaleInfo, TokenValidationResult } from './types'
import { routing } from '@/libs/i18nNavigation'
import { AppConfig } from '@/utils/AppConfig'
import { ADMIN_ROUTE_REGEX, AUTH_COOKIES, STATIC_FILES } from './types'

// Token validation service
export class TokenValidator {
  private static readonly API_URL = process.env.NEXT_PUBLIC_API_URL || ''

  static async validateToken(token: string): Promise<TokenValidationResult> {
    try {
      const response = await fetch(`${this.API_URL}/user/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        return { isValid: false, shouldClearCookies: true }
      }

      const result = await response.json()

      // Check for specific invalid token error
      if (result?.status?.code === 'auth/invalid-token') {
        return { isValid: false, shouldClearCookies: true }
      }

      const isValid = result?.status?.success === true
      return { isValid, shouldClearCookies: !isValid }
    } catch (error) {
      console.error('Token validation failed:', error)
      return { isValid: false, shouldClearCookies: true }
    }
  }
}

// Locale utilities
export class LocaleUtils {
  static extractLocaleFromPath(path: string): LocaleInfo {
    const pathParts = path.split('/').filter(Boolean)
    const firstPart = pathParts[0] || ''
    const isLocaleInPath = routing.locales.includes(firstPart)
    const locale = isLocaleInPath ? firstPart : AppConfig.defaultLocale

    return { locale, isLocaleInPath }
  }

  static createLoginUrl(request: NextRequest, locale: string): URL {
    const loginUrl = new URL(`/${locale}/auth/signin`, request.url)
    loginUrl.searchParams.set('redirect', request.nextUrl.pathname)
    return loginUrl
  }
}

// Route utilities
export class RouteUtils {
  static isStaticFile(path: string): boolean {
    return STATIC_FILES.includes(path as any)
  }

  static isAdminRoute(path: string): boolean {
    return ADMIN_ROUTE_REGEX.test(path)
  }
}

// Cookie utilities
export class CookieUtils {
  static clearAuthCookies(response: NextResponse): void {
    AUTH_COOKIES.forEach((cookieName) => {
      response.cookies.delete(cookieName)
    })
  }

  static getToken(request: NextRequest): string | undefined {
    return request.cookies.get('token')?.value
  }
}
