import type { TemplateConfig } from '../types'
import { TemplateCategory } from '../types'

export const TEMPLATES: TemplateConfig[] = [
  {
    id: 'sport-modern',
    name: '<PERSON><PERSON> thể thao hiện đại',
    category: TemplateCategory.SPORT,
    preview: '/api/placeholder/300/200',
    description: 'Giao diện hiện đại cho đặt sân thể thao với lịch trực quan',
    features: ['Lịch đặt sân', 'Thanh toán online', 'Quản lý khách hàng'],
  },
  {
    id: 'sport-classic',
    name: '<PERSON><PERSON> thể thao cổ điển',
    category: TemplateCategory.SPORT,
    preview: '/api/placeholder/300/200',
    description: '<PERSON>hi<PERSON>t kế cổ điển, dễ sử dụng cho mọi lứa tuổi',
    features: ['Giao diện đơn giản', '<PERSON><PERSON> sử dụng', 'Tương thích mobile'],
  },
  {
    id: 'sport-premium',
    name: '<PERSON><PERSON> thể thao cao cấp',
    category: TemplateCategory.SPORT,
    preview: '/api/placeholder/300/200',
    description: '<PERSON>ia<PERSON> diện sang trọng với nhiều tính năng nâng cao',
    features: ['Thiết kế cao cấp', 'Tính năng nâng cao', 'Tùy chỉnh linh hoạt'],
  },
  {
    id: 'event-simple',
    name: 'Sự kiện đơn giản',
    category: TemplateCategory.EVENT,
    preview: '/api/placeholder/300/200',
    description: 'Thiết kế tối giản cho đặt vé sự kiện',
    features: ['Đặt vé nhanh', 'Quản lý sự kiện', 'Báo cáo chi tiết'],
  },
  {
    id: 'restaurant-booking',
    name: 'Đặt bàn nhà hàng',
    category: TemplateCategory.RESTAURANT,
    preview: '/api/placeholder/300/200',
    description: 'Giao diện chuyên dụng cho đặt bàn nhà hàng',
    features: ['Đặt bàn online', 'Menu tích hợp', 'Quản lý đặt chỗ'],
  },
  {
    id: 'beauty-salon',
    name: 'Salon làm đẹp',
    category: TemplateCategory.BEAUTY,
    preview: '/api/placeholder/300/200',
    description: 'Template dành cho spa và salon làm đẹp',
    features: ['Đặt lịch dịch vụ', 'Quản lý nhân viên', 'Tích hợp thanh toán'],
  },
]

export const TEMPLATE_CATEGORIES = [
  { id: 'all', name: 'Tất cả', count: TEMPLATES.length },
  { id: TemplateCategory.SPORT, name: 'Thể thao', count: TEMPLATES.filter(t => t.category === TemplateCategory.SPORT).length },
  { id: TemplateCategory.EVENT, name: 'Sự kiện', count: TEMPLATES.filter(t => t.category === TemplateCategory.EVENT).length },
  { id: TemplateCategory.RESTAURANT, name: 'Nhà hàng', count: TEMPLATES.filter(t => t.category === TemplateCategory.RESTAURANT).length },
  { id: TemplateCategory.BEAUTY, name: 'Làm đẹp', count: TEMPLATES.filter(t => t.category === TemplateCategory.BEAUTY).length },
]

export const getTemplateById = (id: string): TemplateConfig | undefined => {
  return TEMPLATES.find(template => template.id === id)
}

export const getTemplatesByCategory = (category: TemplateCategory | 'all'): TemplateConfig[] => {
  if (category === 'all') {
    return TEMPLATES
  }
  return TEMPLATES.filter(template => template.category === category)
}
