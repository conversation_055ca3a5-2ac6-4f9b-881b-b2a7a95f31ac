'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import React, { useState } from 'react'

type DateRangePickerProps = {
  defaultDateRange?: { from: Date, to: Date }
  onDateRangeChange: (range: { from: Date, to: Date } | undefined) => void
}

export function DateRangePicker({ defaultDateRange, onDateRangeChange }: DateRangePickerProps) {
  const [date, setDate] = useState(defaultDateRange)

  const handleDateSelect = (selectedDate: { from: Date, to: Date }) => {
    setDate(selectedDate)
    onDateRangeChange(selectedDate)
  }

  return (
    <div className="grid gap-2">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              'w-[300px] justify-start text-left font-normal',
              !date && 'text-muted-foreground',
            )}
          >
            <span className="mr-2">📅</span>
            {date?.from
              ? (
                  date.to
                    ? (
                        <>
                          {format(date.from, 'dd/MM/yyyy', { locale: vi })}
                          {' '}
                          -
                          {' '}
                          {format(date.to, 'dd/MM/yyyy', { locale: vi })}
                        </>
                      )
                    : (
                        format(date.from, 'dd/MM/yyyy', { locale: vi })
                      )
                )
              : (
                  <span>Chọn khoảng thời gian</span>
                )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={(selectedDate) => {
              if (selectedDate?.from && selectedDate?.to) {
                handleDateSelect(selectedDate as { from: Date, to: Date })
              }
            }}
            numberOfMonths={2}
            locale={vi}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
