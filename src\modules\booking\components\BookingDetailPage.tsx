'use client'

import type { BookingDetail } from '@/modules/booking/booking.apis'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { useBookingDetailStore } from '@/modules/booking/stores/booking-detail.store'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  FileText,
  Loader2,
  Mail,
  MapPin,
  Phone,
  User,
  XCircle,
} from 'lucide-react'
import { useEffect } from 'react'

interface BookingDetailPageProps {
  bookingDetail?: BookingDetail
  bookingId?: string
}

const BookingDetailPage: React.FC<BookingDetailPageProps> = ({
  bookingDetail: initialBookingDetail,
  bookingId,
}) => {
  // Use store for client-side loading
  const {
    bookingDetail: storeBookingDetail,
    isLoading,
    error,
    loadBookingDetail,
    setBookingDetail,
  } = useBookingDetailStore()

  // Use initial data if provided, otherwise use store data
  const bookingDetail = initialBookingDetail || storeBookingDetail

  // Load booking detail if bookingId is provided and no initial data
  useEffect(() => {
    if (bookingId && !initialBookingDetail) {
      loadBookingDetail(bookingId)
    } else if (initialBookingDetail) {
      setBookingDetail(initialBookingDetail)
    }
  }, [bookingId, initialBookingDetail, loadBookingDetail, setBookingDetail])

  // Format dates
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: vi })
    } catch {
      return dateString
    }
  }

  const formatDateTime = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: vi })
    } catch {
      return dateString
    }
  }

  // Get status info
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'confirmed':
        return {
          label: 'Đã xác nhận',
          color: 'bg-green-100 text-green-800',
          icon: CheckCircle,
        }
      case 'pending':
        return {
          label: 'Chờ xác nhận',
          color: 'bg-yellow-100 text-yellow-800',
          icon: AlertCircle,
        }
      case 'cancelled':
        return {
          label: 'Đã hủy',
          color: 'bg-red-100 text-red-800',
          icon: XCircle,
        }
      default:
        return {
          label: status,
          color: 'bg-gray-100 text-gray-800',
          icon: AlertCircle,
        }
    }
  }

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price)
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-gray-500">Đang tải thông tin đặt chỗ...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-6">
          <div className="flex justify-center mb-4">
            <AlertCircle className="h-12 w-12 text-red-500" />
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Đã xảy ra lỗi
          </h1>
          <p className="text-gray-600 mb-6">
            {error}
          </p>
          <Button onClick={() => window.location.reload()} className="w-full">
            Thử lại
          </Button>
        </div>
      </div>
    )
  }

  // Show not found state
  if (!bookingDetail) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-6">
          <div className="flex justify-center mb-4">
            <AlertCircle className="h-12 w-12 text-gray-500" />
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Không tìm thấy thông tin đặt chỗ
          </h1>
          <p className="text-gray-600 mb-6">
            Thông tin đặt chỗ không tồn tại hoặc đã bị xóa.
          </p>
        </div>
      </div>
    )
  }

  // Get status info for current booking
  const statusInfo = getStatusInfo(bookingDetail.status)
  const StatusIcon = statusInfo.icon

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-gray-900">
              Chi tiết đặt chỗ
            </h1>
            <Badge className={statusInfo.color}>
              <StatusIcon className="w-4 h-4 mr-1" />
              {statusInfo.label}
            </Badge>
          </div>
          <p className="text-gray-600">
            Mã đặt chỗ:
            {' '}
            <span className="font-mono font-medium">
              #
              {bookingDetail.bookingCode}
            </span>
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Booking Page Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="w-5 h-5 mr-2" />
                  Thông tin trang đặt chỗ
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-semibold text-lg">{bookingDetail.bookingPageName}</p>
                  <p className="text-gray-600">
                    Trang:
                    {' '}
                    <span className="font-mono">
                      /
                      {bookingDetail.slug}
                    </span>
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Booking Slots */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Chi tiết đặt chỗ
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {bookingDetail.bookingSlots.map((slot, index) => (
                    <div key={index} className="border rounded-lg p-4 bg-gray-50">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-2 text-gray-500" />
                          <span className="font-medium">{formatDate(slot.date)}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-2 text-gray-500" />
                          <span>{slot.time}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-2 text-gray-500" />
                          <span>{slot.fieldName}</span>
                        </div>
                      </div>
                      {slot.price && (
                        <div className="mt-2 text-right">
                          <span className="text-sm text-gray-600">Giá: </span>
                          <span className="font-semibold">{formatPrice(slot.price)}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Notes */}
            {bookingDetail.notes && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="w-5 h-5 mr-2" />
                    Ghi chú
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">{bookingDetail.notes}</p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Customer Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  Thông tin khách hàng
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-2 text-gray-500" />
                  <span>{bookingDetail.customerName}</span>
                </div>
                <div className="flex items-center">
                  <Mail className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm">{bookingDetail.customerEmail}</span>
                </div>
                <div className="flex items-center">
                  <Phone className="w-4 h-4 mr-2 text-gray-500" />
                  <span>{bookingDetail.customerPhone}</span>
                </div>
              </CardContent>
            </Card>

            {/* Payment Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="w-5 h-5 mr-2" />
                  Thông tin thanh toán
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Phương thức:</span>
                  <span className="font-medium">{bookingDetail.paymentMethod}</span>
                </div>
                {bookingDetail.quantity && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Số lượng:</span>
                    <span className="font-medium">{bookingDetail.quantity}</span>
                  </div>
                )}
                {bookingDetail.totalPrice && (
                  <>
                    <Separator />
                    <div className="flex justify-between text-lg font-semibold">
                      <span>Tổng cộng:</span>
                      <span className="text-primary">{formatPrice(bookingDetail.totalPrice)}</span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Booking Timeline */}
            <Card>
              <CardHeader>
                <CardTitle>Thời gian</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm">
                  <span className="text-gray-600">Đặt chỗ lúc:</span>
                  <p className="font-medium">{formatDateTime(bookingDetail.createdAt)}</p>
                </div>
                {bookingDetail.updatedAt && (
                  <div className="text-sm">
                    <span className="text-gray-600">Cập nhật lúc:</span>
                    <p className="font-medium">{formatDateTime(bookingDetail.updatedAt)}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => window.print()}
                  >
                    In thông tin đặt chỗ
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => window.open(`/${bookingDetail.slug}`, '_blank')}
                  >
                    Xem trang đặt chỗ
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingDetailPage
