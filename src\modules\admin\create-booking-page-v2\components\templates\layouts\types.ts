import type { BookingConfig, PageInfo } from '../../../types'

// Base Layout Props
export interface BaseLayoutProps {
  config: BookingConfig
  pageInfo: PageInfo
  previewMode?: 'desktop' | 'mobile'
  className?: string
}

// Banner Section Props
export interface BannerSectionProps extends BaseLayoutProps {
  bannerImage?: string
  bannerTitle?: string
  bannerSubtitle?: string
}

// Description Section Props
export interface DescriptionSectionProps extends BaseLayoutProps {
  description?: string
  openTime?: string
  closeTime?: string
  location?: string
}

// Booking Info Section Props
export interface BookingInfoSectionProps extends BaseLayoutProps {
  showPricing?: boolean
  showCapacity?: boolean
  showFieldTypes?: boolean
}

// Grid Slot Section Props
export interface GridSlotSectionProps extends BaseLayoutProps {
  selectedDate?: Date
  onDateChange?: (date: Date) => void
  onSlotSelect?: (fieldId: string, timeSlot: string) => void
  selectedSlots?: Record<string, string[]>
}

// Contact Section Props
export interface ContactSectionProps extends BaseLayoutProps {
  phone?: string
  email?: string
  address?: string
  socialLinks?: {
    facebook?: string
    instagram?: string
    website?: string
  }
}

// Map Section Props
export interface MapSectionProps extends BaseLayoutProps {
  latitude?: number
  longitude?: number
  address?: string
  showDirections?: boolean
}
