import { useRouter } from '@/libs/i18nNavigation'
import { useCallback, useState } from 'react'

export const useNavigation = () => {
  const router = useRouter()
  const [isNavigating, setIsNavigating] = useState(false)

  const navigate = useCallback(async (path: string) => {
    try {
      setIsNavigating(true)
      // Prefetch the destination page
      await router.prefetch(path)
      // Navigate to the page
      await router.push(path)
    } catch (error) {
      console.error('Navigation error:', error)
    } finally {
      setIsNavigating(false)
    }
  }, [router])

  return {
    navigate,
    isNavigating,
  }
}
