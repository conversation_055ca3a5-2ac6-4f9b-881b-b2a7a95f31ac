// Page Information Types
export interface PageInfo {
  name: string
  description: string
  slug: string
}

// Template Configuration Types
export interface TemplateConfig {
  id: string
  name: string
  category: string
  preview: string
  description: string
  features?: string[]
}

// Field Types
export type FieldType = 'football' | 'tennis' | 'badminton' | 'basketball'

export interface BookingField {
  id: string
  name: string
  type: FieldType
  capacity: number
}

// Booking Configuration Types
export interface BookingConfig {
  bannerTitle: string
  bannerSubtitle: string
  bannerImage: string
  openTime: string
  closeTime: string
  fields: BookingField[]
}

// Step Configuration
export interface StepConfig {
  id: number
  title: string
  icon: any // Lucide icon component
  isCompleted: boolean
  isActive: boolean
}

// Template Configuration Types
export interface TemplateConfigData {
  modernSport?: {
    bannerSettings: {
      showOverlay: boolean
      overlayOpacity: number
      textPosition: 'center' | 'left' | 'right'
    }
    layoutSettings: {
      sectionSpacing: 'compact' | 'normal' | 'spacious'
      borderRadius: 'none' | 'small' | 'medium' | 'large'
      shadowLevel: 'none' | 'small' | 'medium' | 'large'
    }
    colorSettings: {
      primaryColor: string
      accentColor: string
      backgroundColor: string
    }
    featureSettings: {
      showPricing: boolean
      showCapacity: boolean
      showRatings: boolean
      showSocialLinks: boolean
      enableQuickBooking: boolean
    }
  }
  classicSport?: {
    layoutSettings: {
      maxWidth: 'narrow' | 'medium' | 'wide'
      sectionSpacing: 'compact' | 'normal' | 'spacious'
    }
    colorSettings: {
      primaryColor: string
      backgroundColor: string
    }
    featureSettings: {
      showAdvancedFilters: boolean
      enableMultiSelect: boolean
      showDetailedInfo: boolean
    }
  }
}

// Main Store State
export interface CreateBookingPageV2State {
  // Current step
  currentStep: number

  // Step data
  pageInfo: PageInfo
  selectedTemplateId: string
  bookingConfig: BookingConfig
  templateConfig: TemplateConfigData

  // UI state
  isLoading: boolean
  errors: Record<string, string>

  // Actions
  setCurrentStep: (step: number) => void
  nextStep: () => void
  prevStep: () => void

  // Page info actions
  updatePageInfo: (info: Partial<PageInfo>) => void
  // Template actions
  setSelectedTemplate: (templateId: string) => void
  updateTemplateConfig: (config: Partial<TemplateConfigData>) => void

  // Booking config actions
  updateBookingConfig: (config: Partial<BookingConfig>) => void
  addField: () => void
  removeField: (fieldId: string) => void
  updateField: (fieldId: string, updates: Partial<BookingField>) => void

  // Validation
  validateCurrentStep: () => boolean
  getStepErrors: (step: number) => string[]

  // Reset
  reset: () => void
}

// Template Category
export enum TemplateCategory {
  SPORT = 'SPORT',
  EVENT = 'EVENT',
  RESTAURANT = 'RESTAURANT',
  BEAUTY = 'BEAUTY',
  EDUCATION = 'EDUCATION',
}

// Validation Rules
export interface ValidationRule {
  field: string
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
}

export interface StepValidation {
  step: number
  rules: ValidationRule[]
}

// Form Data Types for react-hook-form
export interface PageInfoFormData {
  name: string
  description: string
  slug: string
}

export interface DomainConfigFormData {
  subdomain: string
  customDomain?: string
}

export interface BookingConfigFormData {
  bannerTitle: string
  bannerSubtitle: string
  bannerImage: string
  openTime: string
  closeTime: string
  fields: BookingField[]
}
