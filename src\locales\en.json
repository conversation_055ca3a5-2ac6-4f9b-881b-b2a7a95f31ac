{"RootLayout": {"home_link": "Home", "about_link": "About", "counter_link": "Counter", "portfolio_link": "Portfolio", "sign_in_link": "Sign in", "sign_up_link": "Sign up"}, "BaseTemplate": {"description": "Starter code for your Nextjs Boilerplate with Tailwind CSS", "made_with": "Made with <author></author>."}, "Index": {"meta_title": "PickSlot", "meta_description": "PickSlot is a platform for booking sports facilities and restaurants.", "sponsors_title": "Sponsors"}, "Counter": {"meta_title": "Counter", "meta_description": "An example of DB operation", "loading_counter": "Loading counter...", "security_powered_by": "Security, bot detection and rate limiting powered by"}, "CounterForm": {"presentation": "The counter is stored in the database and incremented by the value you provide.", "label_increment": "Increment by", "button_increment": "Increment"}, "CurrentCount": {"count": "Count: {count}"}, "About": {"meta_title": "About", "meta_description": "About page description", "about_paragraph": "Welcome to our About page! We are a team of passionate individuals dedicated to creating amazing software.", "translation_powered_by": "Translation powered by"}, "Portfolio": {"meta_title": "Portfolio", "meta_description": "Welcome to my portfolio page!", "presentation": "Welcome to my portfolio page! Here you will find a carefully curated collection of my work and accomplishments. Through this portfolio, I'm to showcase my expertise, creativity, and the value I can bring to your projects.", "portfolio_name": "Portfolio {name}", "error_reporting_powered_by": "Error reporting powered by", "coverage_powered_by": "Code coverage powered by"}, "PortfolioSlug": {"meta_title": "Portfolio {slug}", "meta_description": "Portfolio {slug} description", "header": "Portfolio {slug}", "content": "Created a set of promotional materials and branding elements for a corporate event. Crafted a visually unified theme, encompassing a logo, posters, banners, and digital assets. Integrated the client's brand identity while infusing it with a contemporary and innovative approach. Garnered favorable responses from event attendees, resulting in a successful event with heightened participant engagement and increased brand visibility.", "log_management_powered_by": "Log management powered by"}, "SignIn": {"meta_title": "Sign in", "meta_description": "Seamlessly sign in to your account with our user-friendly login process."}, "SignUp": {"meta_title": "Sign up", "meta_description": "Effortlessly create an account through our intuitive sign-up process."}, "Dashboard": {"meta_title": "Dashboard", "hello_message": "Hello {email}!", "alternative_message": "Want to build your SaaS faster using the same stack? Try <url></url>."}, "UserProfile": {"meta_title": "User Profile"}, "DashboardLayout": {"dashboard_link": "Dashboard", "user_profile_link": "Manage your account", "sign_out": "Sign out"}, "EmailVerification": {"meta_title": "<PERSON><PERSON><PERSON>", "meta_description": "Verify your email address to activate your account"}, "Terms": {"meta_title": "Terms and Conditions", "meta_description": "Terms and conditions for using PickSlot services"}, "NotFound": {"title": "Page Not Found", "description": "The page you are looking for doesn't exist or has been moved.", "back_button": "Go Back", "home_button": "Go Home", "dashboard_button": "Dashboard", "help_text": "If you believe this is an error, please contact support.", "booking_title": "Booking Page Not Found", "booking_description": "The booking page you are looking for does not exist or has been removed. Please check the URL or contact the booking page owner."}, "BookingPageNotAvailable": {"title": "Booking Page Not Available", "description": "This booking page has not been configured yet or is temporarily unavailable. Please check back later or contact the page owner.", "back_button": "Go Back", "home_button": "Go Home", "help_text": "If you believe this is an error, please contact the booking page owner."}, "Privacy": {"meta_title": "Privacy Policy - PickSlot", "meta_description": "Privacy policy of PickSlot", "title": "CUSTOMER PRIVACY POLICY", "issued_by": "(Issued by PickSlot)", "section1": {"title": "1. PURPOSE", "content": "This policy aims to protect users' personal information when using PickSlot's booking services for venues, tickets, and reservations through our website and application system, while ensuring the company's full compliance with current legal regulations and preventing legal risks related to data collection and processing."}, "section2": {"title": "2. SCOPE OF APPLICATION", "content": "This policy applies to:", "item1": "All users accessing or using the company's platform and services.", "item2": "All personal data collected and processed by PickSlot."}, "section3": {"title": "3. TYPES OF PERSONAL INFORMATION COLLECTED", "content": "Depending on the service, we may collect the following information:", "item1": "Full name, phone number, email address.", "item2": "Location information (if permitted by the user).", "item3": "Booking history, payment information.", "item4": "Device access information (IP, browser type, operating system, etc.).", "item5": "Third-party data (if users log in via Google, Facebook, etc.)."}, "section4": {"title": "4. PURPOSE OF DATA USAGE", "content": "Customer personal information is used for the following purposes:", "item1": "Providing and improving service experience.", "item2": "Sending booking notifications and payment confirmations.", "item3": "Customer support and complaint handling.", "item4": "Analyzing user behavior to optimize services.", "item5": "Ensuring system security and preventing fraud."}, "section5": {"title": "5. DATA SECURITY & STORAGE", "content": "Data is stored on servers located in Vietnam.", "item1": "We implement appropriate technical and organizational measures to ensure data security (encryption, access control, firewalls, etc.).", "item2": "Storage period: maximum 5 years from the last interaction, or as required by current laws."}, "section6": {"title": "6. SHARING WITH THIRD PARTIES", "content": "We do not share personal information with third parties, except in cases of:", "item1": "Explicit customer consent.", "item2": "Requests from competent state authorities.", "item3": "Technical service partners (e.g., payment gateways), bound by strict confidentiality agreements."}, "section7": {"title": "7. <PERSON>US<PERSON><PERSON><PERSON> RIGHTS AND RESPO<PERSON><PERSON><PERSON><PERSON><PERSON>S", "content": "Customers have the right to:", "rights": {"item1": "Request access, modification, or deletion of personal information.", "item2": "Withdraw consent at any time (may affect service usage).", "item3": "File complaints if information is misused."}, "responsibilities": {"title": "Customers are responsible for:", "item1": "Providing accurate information.", "item2": "Not sharing account or personal information with others."}}, "section8": {"title": "8. LEGAL LIABILITY EXEMPTION", "content": "PickSlot is exempt from liability for data breaches occurring due to:", "item1": "Users voluntarily disclosing personal information to third parties.", "item2": "Force majeure events (hackers, natural disasters, third-party software errors...).", "item3": "Users violating Terms of Service."}, "section9": {"title": "9. <PERSON><PERSON><PERSON><PERSON> AMENDMENTS", "content": "We reserve the right to update this Policy and Terms at any time. New versions will be announced on the system and take effect immediately upon publication. Your continued use of our services after updates implies acceptance of the new Terms."}, "section10": {"title": "10. TERMS ACCEPTANCE", "content": "By creating an account or using our services, you confirm that you have read, understood, and agree to comply with these Terms. If you disagree, you may not use our services."}, "section11": {"title": "11. SERVICE USAGE", "content": "You agree to use our services only for lawful purposes and in compliance with all applicable laws and regulations. You may not use our services to engage in any activities that violate others' rights or harm our system."}, "section12": {"title": "12. USER ACCOUNTS", "content": "To access certain service features, you may need to create an account. You are responsible for maintaining the security of your login information and for all activities under your account."}, "section13": {"title": "13. SERVICE TERMINATION", "content": "We reserve the right to suspend or terminate your access to our services at any time if you violate these Terms or engage in activities harmful to our platform."}, "contact": {"title": "CONTACT", "content": "If you have any questions about these Terms, please contact us via email: ……………………………."}, "effective_date": "Policy effective from June 10, 2025"}}