'use client'

import { <PERSON>ert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Env } from '@/libs/Env'
import { setToken } from '@/services/auth'
import { GoogleLogin, GoogleOAuthProvider, useGoogleOneTapLogin } from '@react-oauth/google'
import { motion } from 'framer-motion'
import { Mail } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { authAPIs } from '../auth.apis'

const LoginScreen = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [needsVerification, setNeedsVerification] = useState(false)
  const [unverifiedEmail, setUnverifiedEmail] = useState('')
  const [resendingEmail, setResendingEmail] = useState(false)
  const [emailResent, setEmailResent] = useState(false)
  const router = useRouter()

  // Get redirect URL from query parameters if available
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null)

  // Extract redirect URL from query parameters on component mount
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    const redirect = params.get('redirect')
    if (redirect) {
      setRedirectUrl(redirect)
    }
  }, [])

  // Setup Google One Tap login
  useGoogleOneTapLogin({
    onSuccess: handleGoogleSuccess,
    onError: () => {
      toast.error('Google login failed')
    },
  })

  async function handleGoogleSuccess(response: any) {
    try {
      const payload = {
        idToken: response.credential,
      }
      const result = await authAPIs.googleLogin(payload)

      if (!result?.status?.success) {
        // Check for unverified email error code
        if (result?.status?.code === 'account/unverified-email') {
          // Extract email from the credential or use a default message
          const decodedToken = JSON.parse(atob(response.credential.split('.')[1]))
          const userEmail = decodedToken.email || email

          setUnverifiedEmail(userEmail)
          setNeedsVerification(true)
          return
        }

        throw new Error(result?.status?.message || 'Login failed')
      }

      if (!result?.data?.accessToken) {
        throw new Error('No access token received')
      }

      // Store the token
      setToken(result?.data?.accessToken)

      toast.success('Logged in with Google successfully!')

      // Redirect to the original URL if available, otherwise go to dashboard
      if (redirectUrl) {
        router.push(redirectUrl)
      } else {
        router.push('/admin/dashboard')
      }
    } catch (error: any) {
      toast.error(error.message)
    }
  }

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const payload = {
        email,
        password,
      }

      const result = await authAPIs.login(payload)

      if (!result.status?.success) {
        // Check for unverified email error code
        if (result?.status?.code === 'account/unverified-email') {
          setUnverifiedEmail(email)
          setNeedsVerification(true)
          return
        }

        throw new Error(result?.status.message || 'Login failed')
      }

      if (!result?.data?.accessToken) {
        throw new Error('No access token received')
      }

      // Store the token
      setToken(result?.data?.accessToken)
      toast.success('Logged in successfully!')

      // Redirect to the original URL if available, otherwise go to dashboard
      if (redirectUrl) {
        router.push(redirectUrl)
      } else {
        router.push('/admin/dashboard')
      }
    } catch (error: any) {
      toast.error(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendVerification = async () => {
    if (!unverifiedEmail) {
      return
    }

    setResendingEmail(true)
    setEmailResent(false)

    try {
      const result = await authAPIs.resendVerification({ email: unverifiedEmail })

      if (result?.status?.success) {
        toast.success('Verification email has been resent. Please check your inbox.')
        setEmailResent(true)
      } else {
        throw new Error(result?.status?.message || 'Failed to resend verification email')
      }
    } catch (error: any) {
      toast.error(error.message)
    } finally {
      setResendingEmail(false)
    }
  }

  const renderVerificationMessage = () => {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6 text-center"
      >
        <Alert className="bg-blue-50 border-blue-200">
          <AlertTitle className="text-blue-800">
            <div className="flex justify-center items-center mx-auto mb-2">
              <Mail className="h-5 w-5 text-blue-600" />
              {' '}
              Verify your email
            </div>
          </AlertTitle>
          <AlertDescription className="text-blue-700">
            <div>
              Please check your inbox at
              {' '}
              <span className="font-medium">{unverifiedEmail}</span>
              {' '}
              and click the verification link to activate your account.
            </div>
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          {emailResent
            ? (
                <Alert className="bg-green-50 border-green-200">
                  <AlertDescription className="text-green-700 flex items-center justify-center gap-2">
                    <svg className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Verification email has been sent!
                  </AlertDescription>
                </Alert>
              )
            : (
                <p className="text-sm text-muted-foreground">
                  Didn't receive the email? Check your spam folder or click the button below to resend.
                </p>
              )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              variant="outline"
              className="border-primary/20"
              onClick={() => setNeedsVerification(false)}
            >
              Back to Login
            </Button>
            <Button
              onClick={handleResendVerification}
              disabled={resendingEmail}
            >
              {resendingEmail
                ? (
                    <span className="flex items-center gap-2">
                      <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                      </svg>
                      Sending...
                    </span>
                  )
                : 'Resend Verification Email'}
            </Button>
          </div>
        </div>
      </motion.div>
    )
  }

  const renderLoginWithGoogle = () => {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="flex justify-center pt-2"
      >
        <div className="scale-110">
          <GoogleLogin
            onSuccess={handleGoogleSuccess}
            onError={() => toast.error('Google login failed')}
            // useOneTap
            // auto_select
            theme="filled_blue"
            shape="pill"
          />
        </div>
      </motion.div>
    )
  }

  const renderLoginEmail = () => {
    return (
      <motion.form
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        onSubmit={handleEmailLogin}
        className="space-y-6"
      >
        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            required
            className="h-11 px-4 bg-background/50 border-primary/20 focus:border-primary/40 transition-colors"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password" className="text-sm font-medium">Password</Label>
          <Input
            id="password"
            type="password"
            placeholder="Enter your password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            required
            className="h-11 px-4 bg-background/50 border-primary/20 focus:border-primary/40 transition-colors"
          />
        </div>

        <Button
          type="submit"
          className="w-full h-11 text-base font-medium shadow-lg shadow-primary/20 hover:shadow-primary/10 transition-all duration-300"
          disabled={isLoading}
        >
          {isLoading
            ? (
                <span className="flex items-center gap-2">
                  <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  Signing in...
                </span>
              )
            : 'Sign In'}
        </Button>
      </motion.form>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 via-background to-primary/5 p-4 sm:p-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="backdrop-blur-sm bg-background/95 shadow-xl border-primary/10 p-6 sm:p-8 space-y-8">
          {needsVerification
            ? (
                renderVerificationMessage()
              )
            : (
                <>
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="space-y-3 text-center"
                  >
                    <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">Welcome Back</h1>
                    <p className="text-muted-foreground text-sm">Sign in to your account to continue</p>
                  </motion.div>

                  {renderLoginWithGoogle()}

                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t border-primary/10" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-background px-3 text-muted-foreground font-medium">
                        Or continue with
                      </span>
                    </div>
                  </div>

                  {renderLoginEmail()}

                  <div className="text-center text-sm text-muted-foreground mt-4">
                    Don't have an account?
                    {' '}
                    <Link href="/auth/signup" className="text-primary hover:underline">
                      Sign up
                    </Link>
                  </div>
                </>
              )}
        </Card>
      </motion.div>
    </div>
  )
}

export const LoginScreenWrapper = () => {
  return <GoogleOAuthProvider clientId={Env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}><LoginScreen /></GoogleOAuthProvider>
}

export default LoginScreenWrapper
