import type { Block } from '../create-booking-page/types/blocks'
import type { ThemeSettings } from '../create-booking-page/types/theme'
import mainApi from '@/apis/mainApi'

export interface CreateBookingPagePayload {
  id?: string
  name: string
  description: string
  slug: string
  templateCode: string
  blocks: Block[]
  theme: ThemeSettings
  createdAt?: string
  status?: 'active' | 'inactive' // thêm tùy biến nếu cần
}

export interface BookingPageItem {
  _id: string
  name: string
  description: string
  slug: string
  templateCode: string
  blocks: Block[]
  theme: ThemeSettings
  createdAt: string
  status: 'active' | 'inactive' // thêm tùy biến nếu cần
}

// Admin booking item interface that matches the API response
export interface AdminBookingItem {
  _id: string
  slug: string
  customerName: string
  customerEmail: string
  customerPhone: string
  bookingPageId: string
  bookingDate: string
  bookingSlots: {
    date: string
    field: string
    fieldName: string
    time: string
    _id?: string
  }[]
  paymentMethod: string
  quantity: number
  status: 'pending' | 'confirmed' | 'cancelled'
  createdAt: string
  notes?: string
}

export const bookingPageAPIs = {
  createBookingPage: (data: CreateBookingPagePayload) => mainApi.post('/agent/booking-page', data),
  updateBookingPage: (id: string, data: Partial<CreateBookingPagePayload>) => mainApi.put(`/agent/booking-page/${id}`, data),
  getBookingPages: () => mainApi.get<BookingPageItem[]>('/agent/booking-page'),
  getBookingPageById: (id: string) => mainApi.get<BookingPageItem>(`/agent/booking-page/${id}`),
  deleteBookingPage: (id: string) => mainApi.delete(`/agent/booking-page/${id}`),
  // API cho các tính năng nâng cao
  getBookingPageStats: (id: string) => mainApi.get(`/agent/booking-page/${id}/stats`),
  getBookingPageBookings: (id: string, params?: { status?: string, startDate?: string, endDate?: string, limit?: number, offset?: number }) =>
    mainApi.get<AdminBookingItem[]>(`/agent/booking-page/${id}/bookings`, { params }),
}
