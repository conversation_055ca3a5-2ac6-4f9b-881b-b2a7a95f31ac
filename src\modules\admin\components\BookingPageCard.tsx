'use client'

import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { PAGE_STATUSES, TEMPLATE_NAMES } from '@/modules/admin/constants/mock-data'
import { BarChart2, Calendar, Edit, ExternalLink, Eye, MoreVertical, Trash2 } from 'lucide-react'
import Image from 'next/image'
import React from 'react'
import { bookingPageAPIs } from '../apis/booking-page.api'

/**
 * Thông tin cơ bản của một booking page cho UI
 */
export type BookingPageCardItem = {
  _id: string
  name: string
  status: 'active' | 'inactive' | 'pending'
  template: string
  templateCode?: string
  createdAt: string
  description: string
  avatar: string
  views: number
  bookings: number
  slug: string
}

/**
 * Props cho component BookingPageCard
 */
type BookingPageProps = {
  item: BookingPageCardItem
  onEditAction: (id: string) => void
  onViewStatsAction: (id: string) => void
  onViewLiveAction: (id: string, slug: string) => void
  onOpenControlPanel?: (id: string) => void
}

export const BookingPageCard = ({
  item,
  onEditAction,
  onViewStatsAction,
  onViewLiveAction,
  onOpenControlPanel,
}: BookingPageProps) => {
  const {
    _id,
    name,
    status,
    template,
    createdAt,
    description,
    avatar,
    views,
    bookings,
    slug,
  } = item
  const id = _id

  const statusInfo = PAGE_STATUSES[status]
  const templateName = TEMPLATE_NAMES[template as keyof typeof TEMPLATE_NAMES] || template
  const createdDate = new Date(createdAt).toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })

  const handleDeleteBookingPage = async (id: string) => {
    try {
      await bookingPageAPIs.deleteBookingPage(id)
    } catch (error) {
      console.error('Error deleting booking page:', error)
    }
  }

  return (
    <>
      <TooltipProvider>
        <Card className="overflow-hidden transition-all duration-200 hover:shadow-md group">
          <div className="flex border-b p-4">
            <button
              type="button"
              className="relative w-16 h-16 rounded-md overflow-hidden mr-4 flex-shrink-0 cursor-pointer border-0 p-0"
              onClick={() => onOpenControlPanel ? onOpenControlPanel(id) : onEditAction(id)}
              aria-label={`Control ${name}`}
            >
              <Image
                src={avatar}
                alt={name}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </button>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <button
                  type="button"
                  className="text-lg font-semibold truncate cursor-pointer hover:text-primary transition-colors duration-200 text-left border-0 p-0 bg-transparent"
                  onClick={() => onOpenControlPanel ? onOpenControlPanel(id) : onEditAction(id)}
                  aria-label={`Control ${name}`}
                >
                  {name}
                </button>
                <div className="flex items-center space-x-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-primary"
                        onClick={() => onViewLiveAction(id, slug)}
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span className="sr-only">Xem trang thực tế</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Xem trang thực tế</p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-primary"
                        onClick={() => onEditAction(id)}
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Chỉnh sửa</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Chỉnh sửa</p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-primary"
                        onClick={() => onViewStatsAction(id)}
                      >
                        <BarChart2 className="h-4 w-4" />
                        <span className="sr-only">Xem thống kê</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Xem thống kê</p>
                    </TooltipContent>
                  </Tooltip>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-500">
                        <MoreVertical className="h-4 w-4" />
                        <span className="sr-only">Thêm tùy chọn</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => handleDeleteBookingPage(id)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Xóa
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              <p className="text-sm text-gray-500 truncate">{description}</p>
              <div className="flex items-center mt-2 text-xs text-gray-500">
                <Badge
                  variant="outline"
                  className={`mr-2 bg-${statusInfo.color}-50 text-${statusInfo.color}-700 border-${statusInfo.color}-200`}
                >
                  {statusInfo.label}
                </Badge>
                <span className="mr-3">{templateName}</span>
                <Calendar className="h-3 w-3 mr-1" />
                <span>{createdDate}</span>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 flex justify-between items-center text-sm">
            <div className="flex items-center">
              <Eye className="h-4 w-4 text-gray-500 mr-1" />
              <span>
                {views}
                {' '}
                lượt xem
              </span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 text-gray-500 mr-1" />
              <span>
                {bookings}
                {' '}
                lượt đặt
              </span>
            </div>
            <Button
              size="sm"
              variant="default"
              onClick={() => onOpenControlPanel ? onOpenControlPanel(id) : onEditAction(id)}
              className="transition-all duration-200"
            >
              Quản lý
            </Button>
          </div>
        </Card>
      </TooltipProvider>
    </>
  )
}
