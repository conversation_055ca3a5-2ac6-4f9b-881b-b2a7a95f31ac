# Performance Optimization Tips cho React Forms

## Đã thực hiện:

### 1. React.memo cho components
```tsx
const MemoizedInputField = React.memo<Props>(({ ... }) => (...))
```

### 2. useMemo cho expensive computations
```tsx
const formSchema = useMemo(() => z.object({...}), [fields])
```

### 3. useCallback cho event handlers
```tsx
const handlePaymentMethodSelect = useCallback((method: string) => {
  setValue('paymentMethod', method)
}, [setValue])
```

### 4. Form validation mode
```tsx
useForm({
  mode: 'onBlur', // Thay vì 'onChange'
})
```

## Gợi ý thêm:

### 5. Debounce cho input nếu cần
```tsx
import { useDebouncedCallback } from 'use-debounce'

const debouncedOnChange = useDebouncedCallback(
  (value) => setValue('customerName', value),
  300
)
```

### 6. Lazy loading cho heavy components
```tsx
const HeavyComponent = React.lazy(() => import('./HeavyComponent'))
```

### 7. Virtual scrolling cho long lists
```tsx
import { FixedSizeList as List } from 'react-window'
```

### 8. Sử dụng React.startTransition cho non-urgent updates
```tsx
import { startTransition } from 'react'

startTransition(() => {
  // Non-urgent state updates
})
```

### 9. Optimize bundle size
- Code splitting
- Tree shaking
- Dynamic imports

### 10. Monitor performance
```tsx
import { Profiler } from 'react'

<Profiler id="BookingForm" onRender={onRenderCallback}>
  <BookingForm />
</Profiler>
```
