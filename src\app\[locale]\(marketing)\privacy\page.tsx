import PrivacyScreen from '@/modules/privacy/screens/PrivacyScreen'
import { getTranslations } from 'next-intl/server'

type IPrivacyProps = {
  params: Promise<{ locale: string }>
}

export async function generateMetadata(props: IPrivacyProps) {
  const { locale } = await props.params
  const t = await getTranslations({
    locale,
    namespace: 'Privacy',
  })

  return {
    title: t('meta_title', { fallback: '<PERSON><PERSON>h sách bảo mật thông tin - PickSlot' }),
    description: t('meta_description', { fallback: '<PERSON><PERSON>h sách bảo mật thông tin của PickSlot' }),
  }
}

export default async function Privacy() {
  return <PrivacyScreen />
}
