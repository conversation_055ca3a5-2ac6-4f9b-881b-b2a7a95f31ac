/* eslint-disable jsx-a11y/anchor-is-valid */
'use client'

import { useRouter } from '@/libs/i18nNavigation'
import { appPaths } from '@/utils/app-routes'
import { gsap } from 'gsap'
import Link from 'next/link'
import React, { useEffect, useRef } from 'react'
import { FaFacebook, FaInstagram, FaLinkedin, FaTwitter } from 'react-icons/fa'

export const FooterSection = () => {
  const router = useRouter()
  const sectionRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate footer content
      gsap.from(contentRef.current, {
        y: 50,
        opacity: 0,
        duration: 0.8,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        },
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  const handleNavigation = (path: string) => {
    router.push(path)
  }

  const handleExternalLink = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  return (
    <footer
      ref={sectionRef}
      className="bg-gray-900 text-white py-12"
    >
      <div className="container mx-auto px-4">
        <div ref={contentRef} className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-1">
            <h3 className="text-xl font-bold mb-4">PickSlot</h3>
            <p className="text-gray-400 mb-4">
              Nền tảng đặt chỗ trực tuyến hàng đầu cho doanh nghiệp của bạn.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <FaFacebook size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <FaTwitter size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <FaInstagram size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <FaLinkedin size={20} />
              </a>
            </div>
          </div>

          <div className="col-span-1">
            <h3 className="text-xl font-bold mb-4">Sản phẩm</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="#"
                  className="text-gray-400 hover:text-white transition-colors"
                  onClick={(e) => {
                    e.preventDefault()
                    handleNavigation('/features')
                  }}
                >
                  Tính năng
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-gray-400 hover:text-white transition-colors"
                  onClick={(e) => {
                    e.preventDefault()
                    handleNavigation('/pricing')
                  }}
                >
                  Bảng giá
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-gray-400 hover:text-white transition-colors"
                  onClick={(e) => {
                    e.preventDefault()
                    handleNavigation('/templates')
                  }}
                >
                  Mẫu giao diện
                </a>
              </li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-xl font-bold mb-4">Hỗ trợ</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="#"
                  className="text-gray-400 hover:text-white transition-colors"
                  onClick={(e) => {
                    e.preventDefault()
                    handleNavigation('/help')
                  }}
                >
                  Trung tâm hỗ trợ
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-gray-400 hover:text-white transition-colors"
                  onClick={(e) => {
                    e.preventDefault()
                    handleNavigation('/contact')
                  }}
                >
                  Liên hệ
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-gray-400 hover:text-white transition-colors cursor-pointer"
                  onClick={(e) => {
                    e.preventDefault()
                    handleExternalLink('https://dull-dinosaur-37d.notion.site/C-u-h-i-th-ng-g-p-20e1d6a1d85080ba8d41c4ccd515cc81')
                  }}
                >
                  Câu hỏi thường gặp
                </a>
              </li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-xl font-bold mb-4">Pháp lý</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href={appPaths.marketing.terms()}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  Điều khoản sử dụng
                </Link>
              </li>
              <li>
                <Link
                  href={appPaths.marketing.privacy()}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  Chính sách bảo mật
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>
            ©
            {new Date().getFullYear()}
            {' '}
            PickSlot. Tất cả các quyền được bảo lưu.
          </p>
        </div>
      </div>
    </footer>
  )
}
