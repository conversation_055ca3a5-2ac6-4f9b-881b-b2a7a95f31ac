import { Button } from '@/components/ui/button'
import { Settings } from 'lucide-react'
import React, { memo } from 'react'
import BookingPageReview from '../BookingPageReview'
import QuickConfig from '../QuickConfig'

interface MainContentProps {
  isConfigComplete: boolean
  onOpenSetup: () => void
  onOpenConfigBookingPage: () => void
  contentRef: React.RefObject<HTMLDivElement | null>
  isEditMode?: boolean
}

/**
 * MainContent Component
 * Displays either the preview or a prompt to configure
 */
const MainContent = memo(({
  isConfigComplete,
  onOpenSetup,
  onOpenConfigBookingPage,
  contentRef,
  isEditMode = false,
}: MainContentProps) => (
  <div className="flex-1 bg-gray-100 p-6" ref={contentRef}>
    {isConfigComplete
      ? (
          <div className="space-y-6 animate-fadeIn">
            <div className="flex items-center justify-between pb-4 border-b">
              <QuickConfig />
              <Button
                variant="outline"
                size="sm"
                onClick={onOpenConfigBookingPage}
              >
                Chỉnh sửa cấu hình
              </Button>
            </div>
            <div className="flex justify-center">
              <div className="w-full">
                <BookingPageReview />
              </div>
            </div>
          </div>
        )
      : (
          <div className="flex flex-col items-center justify-center py-12 text-center space-y-4 animate-fadeIn">
            <Settings className="h-16 w-16 text-gray-400" />
            <h2 className="text-xl font-semibold">
              {isEditMode
                ? 'Đang tải cấu hình trang booking'
                : 'Chưa có cấu hình trang booking'}
            </h2>
            <p className="text-gray-500 px-9">
              {isEditMode
                ? 'Vui lòng đợi trong khi chúng tôi tải cấu hình trang booking của bạn.'
                : 'Vui lòng thiết lập cấu hình trang booking để xem trước và tạo trang.'}
            </p>
            <Button
              onClick={onOpenSetup}
              className="mt-4"
            >
              {isEditMode ? 'Mở cấu hình' : 'Thiết lập ngay'}
            </Button>
          </div>
        )}
  </div>
))

MainContent.displayName = 'MainContent'

export default MainContent
