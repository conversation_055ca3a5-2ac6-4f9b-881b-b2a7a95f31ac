import type { ValidationRule } from '../types'
import { ERROR_MESSAGES, RESERVED_SUBDOMAINS, STEP_VALIDATIONS } from '../constants/validation'

interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
}

export const validateStep = (step: number, data: any): ValidationResult => {
  const stepValidation = STEP_VALIDATIONS.find(v => v.step === step)
  if (!stepValidation) {
    return { isValid: true, errors: {} }
  }

  const errors: Record<string, string> = {}

  for (const rule of stepValidation.rules) {
    const fieldError = validateField(rule, data[rule.field], data)
    if (fieldError) {
      errors[rule.field] = fieldError
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

export const validateField = (rule: ValidationRule, value: any, allData?: any): string | null => {
  // Required validation
  if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return ERROR_MESSAGES.REQUIRED
  }

  // Skip other validations if field is empty and not required
  if (!rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return null
  }

  // String validations
  if (typeof value === 'string') {
    // Min length
    if (rule.minLength && value.length < rule.minLength) {
      return ERROR_MESSAGES.MIN_LENGTH(rule.minLength)
    }

    // Max length
    if (rule.maxLength && value.length > rule.maxLength) {
      return ERROR_MESSAGES.MAX_LENGTH(rule.maxLength)
    }

    // Pattern validation
    if (rule.pattern && !rule.pattern.test(value)) {
      return getPatternErrorMessage(rule.field, rule.pattern)
    }

    // Reserved subdomain check
    if (rule.field === 'subdomain' && RESERVED_SUBDOMAINS.includes(value.toLowerCase())) {
      return 'Tên miền này đã được sử dụng, vui lòng chọn tên khác'
    }
  }

  // Custom validation
  if (rule.custom) {
    const customResult = rule.custom(value, allData)
    if (customResult !== true) {
      return typeof customResult === 'string' ? customResult : ERROR_MESSAGES.INVALID_FORMAT
    }
  }

  return null
}

const getPatternErrorMessage = (field: string, pattern: RegExp): string => {
  switch (field) {
    case 'subdomain':
      return 'Tên miền chỉ được chứa chữ cái thường, số và dấu gạch ngang'
    case 'customDomain':
      return ERROR_MESSAGES.INVALID_DOMAIN
    case 'bannerImage':
      return 'URL hình ảnh không hợp lệ (jpg, jpeg, png, gif, webp)'
    case 'openTime':
    case 'closeTime':
      return ERROR_MESSAGES.INVALID_TIME
    default:
      return ERROR_MESSAGES.INVALID_FORMAT
  }
}

// Utility functions for specific validations
export const isValidSubdomain = (subdomain: string): boolean => {
  if (!subdomain || subdomain.length < 3 || subdomain.length > 50) {
    return false
  }
  if (!/^[a-z0-9-]+$/.test(subdomain)) {
    return false
  }
  if (subdomain.startsWith('-') || subdomain.endsWith('-')) {
    return false
  }
  if (subdomain.includes('--')) {
    return false
  }
  if (RESERVED_SUBDOMAINS.includes(subdomain.toLowerCase())) {
    return false
  }
  return true
}

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export const isValidTime = (time: string): boolean => {
  return /^([01]?\d|2[0-3]):[0-5]\d$/.test(time)
}

export const isValidImageUrl = (url: string): boolean => {
  if (!isValidUrl(url)) {
    return false
  }
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(url)
}

// Format validation error messages for display
export const formatValidationErrors = (errors: Record<string, string>): string[] => {
  return Object.entries(errors)
    .filter(([_, message]) => message)
    .map(([field, message]) => `${getFieldDisplayName(field)}: ${message}`)
}

const getFieldDisplayName = (field: string): string => {
  const fieldNames: Record<string, string> = {
    subdomain: 'Tên miền phụ',
    customDomain: 'Tên miền tùy chỉnh',
    selectedTemplateId: 'Mẫu giao diện',
    bannerTitle: 'Tiêu đề banner',
    bannerSubtitle: 'Mô tả banner',
    bannerImage: 'Hình ảnh banner',
    openTime: 'Giờ mở cửa',
    closeTime: 'Giờ đóng cửa',
    fields: 'Danh sách sân',
  }

  return fieldNames[field] || field
}
