import type { BookingConfig, PageInfo } from '../types'
import { useCallback } from 'react'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

// Custom hook with optimized selectors
export const useOptimizedBookingConfig = () => {
  // Use shallow comparison for better performance
  const bookingConfig = useCreateBookingPageV2Store(state => state.bookingConfig)
  const updateBookingConfig = useCreateBookingPageV2Store(state => state.updateBookingConfig)

  // Memoized update function
  const optimizedUpdateConfig = useCallback((updates: Partial<BookingConfig>) => {
    updateBookingConfig(updates)
  }, [updateBookingConfig])

  return {
    bookingConfig,
    updateBookingConfig: optimizedUpdateConfig,
  }
}

export const useOptimizedPageInfo = () => {
  const pageInfo = useCreateBookingPageV2Store(state => state.pageInfo)
  const updatePageInfo = useCreateBookingPageV2Store(state => state.updatePageInfo)

  const optimizedUpdatePageInfo = useCallback((updates: Partial<PageInfo>) => {
    updatePageInfo(updates)
  }, [updatePageInfo])

  return {
    pageInfo,
    updatePageInfo: optimizedUpdatePageInfo,
  }
}

export const useOptimizedTemplate = () => {
  const selectedTemplateId = useCreateBookingPageV2Store(state => state.selectedTemplateId)

  return {
    selectedTemplateId,
  }
}

// Selector for specific fields to avoid unnecessary re-renders
export const useFieldsOnly = () => {
  return useCreateBookingPageV2Store(state => state.bookingConfig.fields)
}

export const useBannerOnly = () => {
  return useCreateBookingPageV2Store(state => ({
    bannerTitle: state.bookingConfig.bannerTitle,
    bannerSubtitle: state.bookingConfig.bannerSubtitle,
    bannerImage: state.bookingConfig.bannerImage,
  }))
}

export const useOperatingHours = () => {
  return useCreateBookingPageV2Store(state => ({
    openTime: state.bookingConfig.openTime,
    closeTime: state.bookingConfig.closeTime,
  }))
}
