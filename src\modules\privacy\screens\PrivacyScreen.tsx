'use client'

import { FooterSection } from '@/modules/home/<USER>/new-home/FooterSection'
import { useTranslations } from 'next-intl'
import React, { useRef } from 'react'
import { PrivacyHeader } from '../components/PrivacyHeader'

export default function PrivacyScreen() {
  const t = useTranslations('Privacy')
  const contentRef = useRef<HTMLDivElement>(null)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <PrivacyHeader />

      {/* Page Header section */}
      <div className="bg-gradient-to-r from-orange-500 to-orange-600 py-16 px-4">
        <div className="container mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-white text-center">
            {t('title')}
          </h1>
        </div>
      </div>

      {/* Content section */}
      <div className="container mx-auto py-12 px-4">
        <div ref={contentRef} className="bg-white rounded-xl shadow-md p-8 space-y-8">
          <p className="text-lg mb-8">
            {t('issued_by')}
          </p>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section1.title')}
            </h2>
            <p className="text-gray-700">{t('section1.content')}</p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section2.title')}
            </h2>
            <p className="text-gray-700 mb-4">{t('section2.content')}</p>
            <ul className="list-decimal pl-6 space-y-2 text-gray-700">
              <li>{t('section2.item1')}</li>
              <li>{t('section2.item2')}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section3.title')}
            </h2>
            <p className="text-gray-700 mb-4">{t('section3.content')}</p>
            <ul className="list-decimal pl-6 space-y-2 text-gray-700">
              <li>{t('section3.item1')}</li>
              <li>{t('section3.item2')}</li>
              <li>{t('section3.item3')}</li>
              <li>{t('section3.item4')}</li>
              <li>{t('section3.item5')}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section4.title')}
            </h2>
            <p className="text-gray-700 mb-4">{t('section4.content')}</p>
            <ul className="list-decimal pl-6 space-y-2 text-gray-700">
              <li>{t('section4.item1')}</li>
              <li>{t('section4.item2')}</li>
              <li>{t('section4.item3')}</li>
              <li>{t('section4.item4')}</li>
              <li>{t('section4.item5')}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section5.title')}
            </h2>
            <p className="text-gray-700 mb-4">{t('section5.content')}</p>
            <ul className="list-decimal pl-6 space-y-2 text-gray-700">
              <li>{t('section5.item1')}</li>
              <li>{t('section5.item2')}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section6.title')}
            </h2>
            <p className="text-gray-700 mb-4">{t('section6.content')}</p>
            <ul className="list-decimal pl-6 space-y-2 text-gray-700">
              <li>{t('section6.item1')}</li>
              <li>{t('section6.item2')}</li>
              <li>{t('section6.item3')}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section7.title')}
            </h2>
            <p className="text-gray-700 mb-4">{t('section7.content')}</p>
            <h3 className="text-xl font-semibold mb-2 text-gray-800">Rights:</h3>
            <ul className="list-decimal pl-6 space-y-2 text-gray-700">
              <li>{t('section7.rights.item1')}</li>
              <li>{t('section7.rights.item2')}</li>
              <li>{t('section7.rights.item3')}</li>
            </ul>
            <h3 className="text-xl font-semibold mt-4 mb-2 text-gray-800">{t('section7.responsibilities.title')}</h3>
            <ul className="list-decimal pl-6 space-y-2 text-gray-700">
              <li>{t('section7.responsibilities.item1')}</li>
              <li>{t('section7.responsibilities.item2')}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section8.title')}
            </h2>
            <p className="text-gray-700 mb-4">{t('section8.content')}</p>
            <ul className="list-decimal pl-6 space-y-2 text-gray-700">
              <li>{t('section8.item1')}</li>
              <li>{t('section8.item2')}</li>
              <li>{t('section8.item3')}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section9.title')}
            </h2>
            <p className="text-gray-700">{t('section9.content')}</p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section10.title')}
            </h2>
            <p className="text-gray-700">{t('section10.content')}</p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section11.title')}
            </h2>
            <p className="text-gray-700">{t('section11.content')}</p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section12.title')}
            </h2>
            <p className="text-gray-700">{t('section12.content')}</p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('section13.title')}
            </h2>
            <p className="text-gray-700">{t('section13.content')}</p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              {t('contact.title')}
            </h2>
            <p className="text-gray-700">{t('contact.content')}</p>
          </section>

          <p className="text-sm text-gray-600 mt-8">
            {t('effective_date')}
          </p>

          {/* Back to top button */}
          <div className="flex justify-center mt-12">
            <button
              type="button"
              className="flex items-center gap-2 bg-orange-500 text-white px-6 py-3 rounded-full hover:bg-orange-600 transition-colors shadow-md"
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              <span>Quay lại đầu trang</span>
            </button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <FooterSection />
    </div>
  )
}
