'use client'

import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { Badge } from '@/components/ui'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Env } from '@/libs/Env'
import { useRouter } from '@/libs/i18nNavigation'
import { appPaths } from '@/utils/app-routes'
import {
  Calendar,
  Edit,
  ExternalLink,
  FileText,
  Settings,
  Zap,
} from 'lucide-react'
import React, { useState } from 'react'
import { FaArrowLeft } from 'react-icons/fa'
import { PAGE_STATUSES } from '../../constants/mock-data'
import BookingPageAdvancedSettings from './BookingPageAdvancedSettings'
import { BookingPageBookings } from './BookingPageBookings'
import BookingPageEdit from './BookingPageEdit'
import BookingPageIntegrations from './BookingPageIntegrations'
import BookingPageOverview from './BookingPageOverview'

export interface BookingPageStats {
  totalViews: number
  totalBookings: number
  conversionRate: number
  averageBookingDuration: number
  popularTimes: string[]
  recentBookings: {
    id: string
    customerName: string
    time: string
    status: string
  }[]
}

export interface BookingPageControlContentProps {
  bookingPageId: string
  bookingPage: BookingPageItem | null
  stats: BookingPageStats | null
  bookings: any[] // Temporary - will be removed as BookingPageBookings now fetches its own data
  onEditAction?: (id: string) => void
  onViewLiveAction?: (id: string, slug: string) => void
}

/**
 * Nội dung của trang quản lý booking page
 */
export const BookingPageControlContent = ({
  bookingPageId,
  bookingPage,
  stats,
  onEditAction,
  onViewLiveAction,
}: BookingPageControlContentProps) => {
  const router = useRouter()

  const [activeTab, setActiveTab] = useState('overview')

  // Chuyển hướng về trang quản lý booking pages
  const handleGoBack = () => {
    router.push(appPaths.admin.manageBookingPages())
  }

  const renderTitle = () => {
    return (
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          onClick={handleGoBack}
          className="h-9 w-9 flex-shrink-0"
        >
          <FaArrowLeft className="h-4 w-4" />
        </Button>

        <div className="text-xl md:text-2xl font-bold truncate">
          {bookingPage?.name || 'Booking Page'}
        </div>
      </div>
    )
  }

  const statusInfo = PAGE_STATUSES[bookingPage?.status ?? 'inactive']

  return (
    <div className="flex flex-col space-y-6">
      <Card className="px-6 py-4 md:py-6">
        {renderTitle()}

        <div className="flex flex-col items-center space-y-4 md:flex-row md:space-y-0 md:justify-between">
          <div className="flex flex-wrap md:flex items-center gap-2">
            <Badge
              variant="outline"
              className={`bg-${statusInfo.color}-50 text-${statusInfo.color}-700 border-${statusInfo.color}-200`}
            >
              {statusInfo.label}
            </Badge>
            <div className="text-sm text-gray-500">
              <span className="text-xs text-gray-500">
                {`${Env.NEXT_PUBLIC_DOMAIN}/${bookingPage?.slug}`}
              </span>
            </div>
          </div>

          <div className="flex gap-2 justify-start">
            {onViewLiveAction && bookingPage?.slug && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewLiveAction(bookingPageId, bookingPage.slug)}
                className="flex items-center gap-1"
              >
                <ExternalLink className="h-4 w-4" />
                Xem trang
              </Button>
            )}
            {onEditAction && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEditAction(bookingPageId)}
                className="flex items-center gap-1"
              >
                <Edit className="h-4 w-4" />
                Chỉnh sửa
              </Button>
            )}
          </div>
        </div>

      </Card>

      <Card className="overflow-hidden">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="flex flex-1 flex-col"
        >
          <div className="overflow-x-auto">
            <TabsList
              className="md:grid md:grid-cols-5 inline-flex w-auto min-w-full md:w-full h-12 px-6 py-3 border-b rounded-none"
            >
              <TabsTrigger value="overview" className="flex items-center gap-1 whitespace-nowrap">
                <FileText className="h-4 w-4" />
                <span className="hidden md:inline">Tổng quan</span>
                <span className="sr-only md:hidden">Tổng quan</span>
              </TabsTrigger>
              <TabsTrigger value="bookings" className="flex items-center gap-1 whitespace-nowrap">
                <Calendar className="h-4 w-4" />
                <span className="hidden md:inline">Đặt lịch</span>
                <span className="sr-only md:hidden">Đặt lịch</span>
              </TabsTrigger>
              <TabsTrigger value="edit" className="flex items-center gap-1 whitespace-nowrap">
                <Edit className="h-4 w-4" />
                <span className="hidden md:inline">Cấu hình</span>
                <span className="sr-only md:hidden">Cấu hình</span>
              </TabsTrigger>
              <TabsTrigger value="advanced" className="flex items-center gap-1 whitespace-nowrap">
                <Settings className="h-4 w-4" />
                <span className="hidden md:inline">Nâng cao</span>
                <span className="sr-only md:hidden">Nâng cao</span>
              </TabsTrigger>
              <TabsTrigger value="integrations" className="flex items-center gap-1 whitespace-nowrap">
                <Zap className="h-4 w-4" />
                <span className="hidden md:inline">Tích hợp</span>
                <span className="sr-only md:hidden">Tích hợp</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="p-4 md:p-6">
            <TabsContent value="overview" className="mt-0">
              <BookingPageOverview
                bookingPage={bookingPage as any}
                stats={stats}
              />
            </TabsContent>

            <TabsContent value="bookings" className="mt-0">
              <BookingPageBookings bookingPageId={bookingPageId} />
            </TabsContent>

            <TabsContent value="edit" className="mt-0">
              <BookingPageEdit
                bookingPageId={bookingPageId}
                bookingPage={bookingPage as any}
                onUpdate={async (updatedData) => {
                  // Trong môi trường thực tế, gọi API để cập nhật
                  // eslint-disable-next-line no-console
                  console.log('Updating booking page:', updatedData)
                  // Giả lập API thành công
                  return true
                }}
              />
            </TabsContent>

            <TabsContent value="advanced" className="mt-0">
              <BookingPageAdvancedSettings
                bookingPageId={bookingPageId}
                bookingPage={bookingPage as any}
              />
            </TabsContent>

            <TabsContent value="integrations" className="mt-0">
              <BookingPageIntegrations
                bookingPageId={bookingPageId}
                bookingPage={bookingPage as any}
              />
            </TabsContent>
          </div>
        </Tabs>
      </Card>
    </div>
  )
}

export default BookingPageControlContent
