'use client'

import type { TemplateCategory } from '../../types'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Check, Filter, Palette } from 'lucide-react'
import React, { useState } from 'react'
import { getTemplatesByCategory, TEMPLATE_CATEGORIES, TEMPLATES } from '../../constants/templates'
import { useCreateBookingPageV2Store } from '../../stores/create-booking-page-v2.store'

export const TemplateSelectionStep: React.FC = () => {
  const { selectedTemplateId, setSelectedTemplate, errors } = useCreateBookingPageV2Store()
  const [activeCategory, setActiveCategory] = useState<TemplateCategory | 'all'>('all')

  const filteredTemplates = getTemplatesByCategory(activeCategory)
  const selectedTemplate = TEMPLATES.find(t => t.id === selectedTemplateId)

  return (
    <div className="animate-in fade-in slide-in-from-right-4 duration-300">
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
        {/* Template List */}
        <Card className="bg-white/90 backdrop-blur-md border-orange-200/50 shadow-2xl">
          <CardHeader className="px-6 py-6">
            <CardTitle className="text-xl lg:text-2xl text-gray-900 flex items-center gap-3">
              <Palette className="w-6 h-6 text-orange-500" />
              Chọn mẫu giao diện
            </CardTitle>
            <p className="text-base text-gray-600 mt-2">
              Chọn mẫu phù hợp với loại hình kinh doanh của bạn
            </p>

            {errors.selectedTemplateId && (
              <p className="text-sm text-red-600 mt-2">{errors.selectedTemplateId}</p>
            )}
          </CardHeader>

          <CardContent className="px-6 pb-6">
            {/* Category Filter */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-3">
                <Filter className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Lọc theo danh mục:</span>
              </div>
              <div className="flex flex-nowrap overflow-x-auto gap-2 py-4">
                {TEMPLATE_CATEGORIES.map(category => (
                  <Button
                    key={category.id}
                    variant={activeCategory === category.id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setActiveCategory(category.id as any)}
                    className={`text-xs ${
                      activeCategory === category.id
                        ? 'bg-orange-500 hover:bg-orange-600 text-white'
                        : 'border-orange-300 text-orange-600 hover:bg-orange-50'
                    }`}
                  >
                    {category.name}
                    {' '}
                    (
                    {category.count}
                    )
                  </Button>
                ))}
              </div>
            </div>

            {/* Template List */}
            <div className="space-y-4 max-h-[50vh] overflow-y-auto overflow-x-hidden px-4 py-4">
              {filteredTemplates.map(template => (
                <div
                  key={template.id}
                  className={`
                    p-4 lg:p-5 border-2 rounded-xl cursor-pointer transition-all duration-300 hover:scale-[1.02]
                    ${selectedTemplateId === template.id
                  ? 'border-orange-400 bg-gradient-to-r from-orange-50 to-orange-100 shadow-lg'
                  : 'border-gray-200 hover:border-orange-300 hover:bg-orange-25 hover:shadow-md'
                }
                  `}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <div className="flex items-start gap-4">
                    <div className="w-20 h-14 lg:w-24 lg:h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0 shadow-sm">
                      <img
                        src={template.preview}
                        alt={template.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2 mb-2">
                        <div className="flex items-center gap-2 flex-wrap">
                          <h3 className="font-semibold text-gray-900 text-base lg:text-lg">
                            {template.name}
                          </h3>
                          <Badge variant="secondary" className="text-xs px-2 py-1">
                            {template.category}
                          </Badge>
                        </div>
                        {selectedTemplateId === template.id && (
                          <Check className="w-5 h-5 text-orange-500 flex-shrink-0" />
                        )}
                      </div>
                      <p className="text-sm lg:text-base text-gray-600 leading-relaxed mb-2">
                        {template.description}
                      </p>
                      {template.features && (
                        <div className="flex flex-wrap gap-1">
                          {template.features.slice(0, 3).map((feature, index) => (
                            <span
                              key={index}
                              className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded"
                            >
                              {feature}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Template Preview */}
        <Card className="bg-white/90 backdrop-blur-md border-orange-200/50 shadow-2xl">
          <CardHeader className="px-6 py-6">
            <CardTitle className="text-xl lg:text-2xl text-gray-900">Xem trước giao diện</CardTitle>
            <p className="text-base text-gray-600 mt-2">
              {selectedTemplate
                ? `Xem trước mẫu: ${selectedTemplate.name}`
                : 'Chọn một mẫu để xem trước'}
            </p>
          </CardHeader>
          <CardContent className="px-6 pb-6">
            <div className="aspect-[4/3] bg-gray-100 rounded-xl border-2 border-dashed border-gray-300 flex items-center justify-center overflow-hidden">
              {selectedTemplate
                ? (
                    <div className="w-full h-full bg-white rounded-lg shadow-sm overflow-hidden">
                      <img
                        src={selectedTemplate.preview}
                        alt="Template preview"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )
                : (
                    <div className="text-center text-gray-500">
                      <Palette className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium">Chọn mẫu để xem trước</p>
                      <p className="text-sm mt-1">Giao diện sẽ hiển thị ở đây</p>
                    </div>
                  )}
            </div>

            {/* Template Details */}
            {/* {selectedTemplate && (
              <div className="mt-6 p-4 bg-orange-50 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-2">Tính năng chính:</h4>
                <ul className="space-y-1">
                  {selectedTemplate.features?.map((feature, index) => (
                    <li key={index} className="text-sm text-orange-700 flex items-center gap-2">
                      <Check className="w-3 h-3" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            )} */}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
