'use client'

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  AlertCircle,
  Calendar,
  Edit,
  ExternalLink,
  FileText,
  Loader2,
  Settings,
  Zap,
} from 'lucide-react'
import React, { useState } from 'react'
import useBookingPageDetail from '../hooks/useBookingPageDetail'
import BookingPageAdvancedSettings from './BookingPageControlPanel/BookingPageAdvancedSettings'
import { BookingPageBookings } from './BookingPageControlPanel/BookingPageBookings'
import BookingPageEdit from './BookingPageControlPanel/BookingPageEdit'
import BookingPageIntegrations from './BookingPageControlPanel/BookingPageIntegrations'
import BookingPageOverview from './BookingPageControlPanel/BookingPageOverview'

export type BookingPageControlPanelProps = {
  isOpen: boolean
  onClose: () => void
  bookingPageId: string
  onEditAction?: (id: string) => void
  onViewLiveAction?: (id: string, slug: string) => void
}

/**
 * Control panel for managing a booking page
 * Opens when a user clicks on a booking page card
 */
export const BookingPageControlPanel = ({
  isOpen,
  onClose,
  bookingPageId,
  onEditAction,
  onViewLiveAction,
}: BookingPageControlPanelProps) => {
  const [activeTab, setActiveTab] = useState('overview')
  const { data, isLoading, error, fetchBookingPageDetails, updateBookingPage } = useBookingPageDetail(bookingPageId)

  // Lấy thông tin booking page từ data
  const bookingPage = data.bookingPage || {
    id: bookingPageId,
    name: 'Đang tải...',
    status: 'active' as const,
    template: 'sport-field-continuous',
    templateCode: 'SPORT_FIELD_CONTINUOUS',
    createdAt: new Date().toISOString(),
    description: 'Đang tải thông tin...',
    avatar: 'https://placehold.co/100x100?text=BP',
    views: 0,
    bookings: 0,
    slug: 'loading',
    blocks: [],
    theme: {
      primaryColor: '#2563eb',
      fontFamily: 'Inter, sans-serif',
      layout: 'vertical' as const,
    },
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="max-w-5xl w-full h-[90vh] max-h-[90vh] flex flex-col p-0 gap-0">
        <DialogHeader className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl flex items-center gap-2">
              {isLoading
                ? (
                    <>
                      <Loader2 className="h-5 w-5 animate-spin" />
                      <span>Đang tải thông tin...</span>
                    </>
                  )
                : (
                    <>
                      Quản lý Booking Page:
                      {' '}
                      {bookingPage.name}
                    </>
                  )}
            </DialogTitle>
            <div className="flex gap-2">
              {onViewLiveAction && bookingPage.slug && bookingPage.slug !== 'loading' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewLiveAction(bookingPageId, bookingPage.slug)}
                  className="flex items-center gap-1"
                  disabled={isLoading}
                >
                  <ExternalLink className="h-4 w-4" />
                  Xem trang
                </Button>
              )}
              {onEditAction && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEditAction(bookingPageId)}
                  className="flex items-center gap-1"
                  disabled={isLoading}
                >
                  <Edit className="h-4 w-4" />
                  Chỉnh sửa
                </Button>
              )}
            </div>
          </div>
        </DialogHeader>

        {error
          ? (
              <div className="p-6">
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Lỗi</AlertTitle>
                  <AlertDescription>
                    {error}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchBookingPageDetails()}
                      className="mt-2 ml-2"
                    >
                      Thử lại
                    </Button>
                  </AlertDescription>
                </Alert>
              </div>
            )
          : (
              <div className="flex flex-1 overflow-hidden">
                <Tabs
                  value={activeTab}
                  onValueChange={setActiveTab}
                  className="flex flex-1 flex-col h-full"
                >
                  <TabsList className="grid grid-cols-5 h-12 px-6 py-3 border-b">
                    <TabsTrigger value="overview" className="flex items-center gap-1" disabled={isLoading}>
                      <FileText className="h-4 w-4" />
                      Tổng quan
                    </TabsTrigger>
                    <TabsTrigger value="bookings" className="flex items-center gap-1" disabled={isLoading}>
                      <Calendar className="h-4 w-4" />
                      Đặt lịch
                    </TabsTrigger>
                    <TabsTrigger value="edit" className="flex items-center gap-1" disabled={isLoading}>
                      <Edit className="h-4 w-4" />
                      Cấu hình
                    </TabsTrigger>
                    <TabsTrigger value="advanced" className="flex items-center gap-1" disabled={isLoading}>
                      <Settings className="h-4 w-4" />
                      Nâng cao
                    </TabsTrigger>
                    <TabsTrigger value="integrations" className="flex items-center gap-1" disabled={isLoading}>
                      <Zap className="h-4 w-4" />
                      Tích hợp
                    </TabsTrigger>
                  </TabsList>

                  {isLoading
                    ? (
                        <div className="flex-1 flex items-center justify-center">
                          <div className="flex flex-col items-center gap-2">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            <p className="text-sm text-gray-500">Đang tải thông tin booking page...</p>
                          </div>
                        </div>
                      )
                    : (
                        <div className="flex-1 overflow-auto p-6">
                          <TabsContent value="overview" className="h-full mt-0">
                            <BookingPageOverview
                              bookingPage={bookingPage as any}
                              stats={data.stats}
                            />
                          </TabsContent>

                          <TabsContent value="bookings" className="h-full mt-0">
                            <BookingPageBookings
                              bookingPageId={bookingPageId}
                            />
                          </TabsContent>

                          <TabsContent value="edit" className="h-full mt-0">
                            <BookingPageEdit
                              bookingPageId={bookingPageId}
                              bookingPage={bookingPage as any}
                              onUpdate={updateBookingPage}
                            />
                          </TabsContent>

                          <TabsContent value="advanced" className="h-full mt-0">
                            <BookingPageAdvancedSettings
                              bookingPageId={bookingPageId}
                              bookingPage={bookingPage as any}
                            />
                          </TabsContent>

                          <TabsContent value="integrations" className="h-full mt-0">
                            <BookingPageIntegrations
                              bookingPageId={bookingPageId}
                              bookingPage={bookingPage as any}
                            />
                          </TabsContent>
                        </div>
                      )}
                </Tabs>
              </div>
            )}
      </DialogContent>
    </Dialog>
  )
}

export default BookingPageControlPanel
