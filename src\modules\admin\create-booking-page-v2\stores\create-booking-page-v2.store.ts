import type { BookingConfig, BookingField, CreateBookingPageV2State, PageInfo } from '../types'
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { validateStep } from '../utils/validation'

const initialPageInfo: PageInfo = {
  name: '',
  description: '',
  slug: '',
}

const initialBookingConfig: BookingConfig = {
  bannerTitle: '',
  bannerSubtitle: '',
  bannerImage: '',
  openTime: '06:00',
  closeTime: '22:00',
  fields: [],
}

export const useCreateBookingPageV2Store = create<CreateBookingPageV2State>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        currentStep: 1,
        pageInfo: initialPageInfo,
        selectedTemplateId: '',
        bookingConfig: initialBookingConfig,
        isLoading: false,
        errors: {},

        // Step navigation
        setCurrentStep: (step: number) => {
          set({ currentStep: step }, false, 'setCurrentStep')
        },

        nextStep: () => {
          const { currentStep } = get()
          if (currentStep < 3) {
            set({ currentStep: currentStep + 1, errors: {} }, false, 'nextStep')
          }
        },

        prevStep: () => {
          const { currentStep } = get()
          if (currentStep > 1) {
            set({ currentStep: currentStep - 1, errors: {} }, false, 'prevStep')
          }
        },

        // Page info actions
        updatePageInfo: (info: Partial<PageInfo>) => {
          set(
            state => ({
              pageInfo: { ...state.pageInfo, ...info },
              errors: { ...state.errors, ...Object.keys(info).reduce((acc, key) => ({ ...acc, [key]: '' }), {}) },
            }),
            false,
            'updatePageInfo',
          )
        },

        // Template actions
        setSelectedTemplate: (templateId: string) => {
          set(
            { selectedTemplateId: templateId, errors: { ...get().errors, selectedTemplateId: '' } },
            false,
            'setSelectedTemplate',
          )
        },

        // Booking config actions
        updateBookingConfig: (config: Partial<BookingConfig>) => {
          set(
            state => ({
              bookingConfig: { ...state.bookingConfig, ...config },
              errors: { ...state.errors, ...Object.keys(config).reduce((acc, key) => ({ ...acc, [key]: '' }), {}) },
            }),
            false,
            'updateBookingConfig',
          )
        },

        addField: () => {
          const { bookingConfig } = get()
          const newField: BookingField = {
            id: `field-${Date.now()}`,
            name: `Sân ${bookingConfig.fields.length + 1}`,
            type: 'football',
            capacity: 1,
          }

          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: [...state.bookingConfig.fields, newField],
              },
              errors: { ...state.errors, fields: '' },
            }),
            false,
            'addField',
          )
        },

        removeField: (fieldId: string) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: state.bookingConfig.fields.filter(field => field.id !== fieldId),
              },
            }),
            false,
            'removeField',
          )
        },

        updateField: (fieldId: string, updates: Partial<BookingField>) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: state.bookingConfig.fields.map(field =>
                  field.id === fieldId ? { ...field, ...updates } : field,
                ),
              },
            }),
            false,
            'updateField',
          )
        },

        // Validation
        validateCurrentStep: () => {
          const { currentStep, pageInfo, selectedTemplateId, bookingConfig } = get()

          let data: any
          switch (currentStep) {
            case 1:
              data = pageInfo
              break
            case 2:
              data = { selectedTemplateId }
              break
            case 3:
              data = bookingConfig
              break
            default:
              return false
          }

          // if (!isValid) {
          //   set({ errors }, false, 'validateCurrentStep')
          // }

          return true
        },

        getStepErrors: (step: number) => {
          const { pageInfo, selectedTemplateId, bookingConfig } = get()

          let data: any
          switch (step) {
            case 1:
              data = pageInfo
              break
            case 2: // Fixed: was case 3 before
              data = { selectedTemplateId }
              break
            case 3: // Fixed: was case 4 before
              data = bookingConfig
              break
            default:
              return []
          }

          const { errors } = validateStep(step, data)
          return Object.values(errors).filter(Boolean) as string[]
        },

        // Reset
        reset: () => {
          set(
            {
              currentStep: 1,
              pageInfo: initialPageInfo,
              selectedTemplateId: '',
              bookingConfig: initialBookingConfig,
              isLoading: false,
              errors: {},
            },
            false,
            'reset',
          )
        },

        // Clear localStorage
        clearStorage: () => {
          localStorage.removeItem('create-booking-page-v2-store')
          get().reset()
        },
      }),
      {
        name: 'create-booking-page-v2-store',
        // Chỉ persist những state cần thiết, không persist errors và isLoading
        partialize: state => ({
          currentStep: state.currentStep,
          pageInfo: state.pageInfo,
          selectedTemplateId: state.selectedTemplateId,
          bookingConfig: state.bookingConfig,
        }),
        // Version để handle migration khi structure thay đổi
        version: 1,
        migrate: (persistedState: any, version: number) => {
          // Handle migration logic nếu cần
          if (version === 0) {
            // Migration from version 0 to 1
            return {
              ...persistedState,
              // Add any new fields or transformations
            }
          }
          return persistedState as CreateBookingPageV2State
        },
      },
    ),
    {
      name: 'create-booking-page-v2-store',
    },
  ),
)
