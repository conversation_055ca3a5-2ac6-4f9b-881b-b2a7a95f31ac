import { Button } from '@/components/ui/button'
import { Calendar, Plus } from 'lucide-react'
import React from 'react'

interface EmptyStateProps {
  hasFilters: boolean
  onCreateNew: () => void
  onClearFilters?: () => void
}

/**
 * Component to display when no bookings are found
 */
export const EmptyState = ({ hasFilters, onCreateNew, onClearFilters }: EmptyStateProps) => {
  if (hasFilters) {
    return (
      <div className="text-center py-12">
        <Calendar className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Không tìm thấy đặt lịch</h3>
        <p className="mt-1 text-sm text-gray-500">
          Không có đặt lịch nào phù hợp với bộ lọc hiện tại.
        </p>
        <div className="mt-6 space-x-3">
          {onClearFilters && (
            <Button variant="outline" onClick={onClearFilters}>
              Xóa bộ lọc
            </Button>
          )}
          <Button onClick={onCreateNew}>
            <Plus className="h-4 w-4 mr-2" />
            Tạo đặt lịch mới
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="text-center py-12">
      <Calendar className="mx-auto h-12 w-12 text-gray-400" />
      <h3 className="mt-2 text-sm font-medium text-gray-900">Chưa có đặt lịch nào</h3>
      <p className="mt-1 text-sm text-gray-500">
        Bắt đầu bằng cách tạo đặt lịch thủ công hoặc chờ khách hàng đặt lịch.
      </p>
      <div className="mt-6">
        <Button onClick={onCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          Tạo đặt lịch mới
        </Button>
      </div>
    </div>
  )
}

export default EmptyState
