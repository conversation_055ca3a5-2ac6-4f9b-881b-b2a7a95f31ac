import { StepValidation, ValidationRule } from '../types'

// Validation rules for each step
export const STEP_VALIDATIONS: StepValidation[] = [
  // Step 1: Page Information
  {
    step: 1,
    rules: [
      {
        field: 'name',
        required: true,
        minLength: 3,
        maxLength: 100
      },
      {
        field: 'description',
        required: true,
        minLength: 10,
        maxLength: 500
      },
      {
        field: 'slug',
        required: true,
        minLength: 3,
        maxLength: 50,
        pattern: /^[a-z0-9-]+$/,
        custom: (value: string) => {
          if (value.startsWith('-') || value.endsWith('-')) {
            return 'Đường dẫn không được bắt đầu hoặc kết thúc bằng dấu gạch ngang'
          }
          if (value.includes('--')) {
            return 'Đường dẫn không được chứa hai dấu gạch ngang liên tiếp'
          }
          return true
        }
      }
    ]
  },

  // Step 2: Domain Configuration
  {
    step: 2,
    rules: [
      {
        field: 'subdomain',
        required: true,
        minLength: 3,
        maxLength: 50,
        pattern: /^[a-z0-9-]+$/,
        custom: (value: string) => {
          if (value.startsWith('-') || value.endsWith('-')) {
            return 'Tên miền không được bắt đầu hoặc kết thúc bằng dấu gạch ngang'
          }
          if (value.includes('--')) {
            return 'Tên miền không được chứa hai dấu gạch ngang liên tiếp'
          }
          return true
        }
      },
      {
        field: 'customDomain',
        required: false,
        pattern: /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
      }
    ]
  },

  // Step 3: Template Selection
  {
    step: 3,
    rules: [
      {
        field: 'selectedTemplateId',
        required: true,
        custom: (value: string) => {
          if (!value || value.trim() === '') {
            return 'Vui lòng chọn một mẫu giao diện'
          }
          return true
        }
      }
    ]
  },

  // Step 4: Booking Configuration
  {
    step: 4,
    rules: [
      {
        field: 'bannerTitle',
        required: true,
        minLength: 5,
        maxLength: 100
      },
      {
        field: 'bannerSubtitle',
        required: false,
        maxLength: 200
      },
      {
        field: 'bannerImage',
        required: false,
        pattern: /^(https?:\/\/).*\.(jpg|jpeg|png|gif|webp)$/i
      },
      {
        field: 'openTime',
        required: true,
        pattern: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
      },
      {
        field: 'closeTime',
        required: true,
        pattern: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
        custom: (value: string, allData: any) => {
          if (allData.openTime && value <= allData.openTime) {
            return 'Giờ đóng cửa phải sau giờ mở cửa'
          }
          return true
        }
      },
      {
        field: 'fields',
        required: true,
        custom: (fields: any[]) => {
          if (!fields || fields.length === 0) {
            return 'Vui lòng thêm ít nhất một sân'
          }
          
          // Check for duplicate field names
          const names = fields.map(f => f.name.toLowerCase().trim())
          const uniqueNames = new Set(names)
          if (names.length !== uniqueNames.size) {
            return 'Tên sân không được trùng lặp'
          }
          
          // Check each field
          for (const field of fields) {
            if (!field.name || field.name.trim().length < 2) {
              return 'Tên sân phải có ít nhất 2 ký tự'
            }
            if (field.capacity < 1 || field.capacity > 100) {
              return 'Sức chứa phải từ 1 đến 100'
            }
          }
          
          return true
        }
      }
    ]
  }
]

// Error messages
export const ERROR_MESSAGES = {
  REQUIRED: 'Trường này là bắt buộc',
  MIN_LENGTH: (min: number) => `Tối thiểu ${min} ký tự`,
  MAX_LENGTH: (max: number) => `Tối đa ${max} ký tự`,
  INVALID_FORMAT: 'Định dạng không hợp lệ',
  INVALID_DOMAIN: 'Tên miền không hợp lệ',
  INVALID_URL: 'URL không hợp lệ',
  INVALID_TIME: 'Thời gian không hợp lệ (HH:MM)',
  TEMPLATE_REQUIRED: 'Vui lòng chọn một mẫu giao diện',
  FIELDS_REQUIRED: 'Vui lòng thêm ít nhất một sân'
}

// Reserved subdomains that cannot be used
export const RESERVED_SUBDOMAINS = [
  'www', 'api', 'admin', 'app', 'mail', 'ftp', 'blog', 'shop', 'store',
  'support', 'help', 'docs', 'dev', 'test', 'staging', 'demo', 'beta'
]
