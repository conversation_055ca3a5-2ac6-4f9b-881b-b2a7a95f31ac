import type { AdminBookingItem } from '@/modules/admin/apis/booking-page.api'
import type { BookingFilters } from '../types'

/**
 * Filter bookings based on tab selection
 * @param booking - Booking item
 * @param bookingTab - Selected tab
 * @returns Whether booking matches tab filter
 */
export const filterByTab = (booking: AdminBookingItem, bookingTab: BookingFilters['bookingTab']) => {
  if (bookingTab === 'all') {
    return true
  }
  return booking.status === bookingTab
}

/**
 * Filter bookings based on search query
 * @param booking - Booking item
 * @param searchQuery - Search query string
 * @returns Whether booking matches search query
 */
export const filterBySearch = (booking: AdminBookingItem, searchQuery: string) => {
  if (!searchQuery) {
    return true
  }

  const query = searchQuery.toLowerCase()
  return (
    booking.customerName.toLowerCase().includes(query)
    || booking.customerPhone.includes(searchQuery)
    || booking.customerEmail.toLowerCase().includes(query)
  )
}

/**
 * Filter bookings based on status
 * @param booking - Booking item
 * @param statusFilter - Status filter
 * @returns Whether booking matches status filter
 */
export const filterByStatus = (booking: AdminBookingItem, statusFilter: BookingFilters['statusFilter']) => {
  if (statusFilter === 'all') {
    return true
  }
  return booking.status === statusFilter
}

/**
 * Filter bookings based on date range
 * @param booking - Booking item
 * @param dateFilter - Date filter
 * @returns Whether booking matches date filter
 */
export const filterByDate = (booking: AdminBookingItem, dateFilter: BookingFilters['dateFilter']) => {
  if (dateFilter === 'all') {
    return true
  }

  const today = new Date().toISOString().split('T')[0]
  const tomorrow = new Date(Date.now() + 86400000).toISOString().split('T')[0]

  switch (dateFilter) {
    case 'today':
      return booking.bookingDate === today
    case 'tomorrow':
      return booking.bookingDate === tomorrow
    case 'thisWeek':
      return filterByThisWeek(booking.bookingDate)
    case 'thisMonth':
      return filterByThisMonth(booking.bookingDate)
    default:
      return true
  }
}

/**
 * Check if booking date is in current week
 * @param bookingDate - Booking date string
 * @returns Whether date is in current week
 */
const filterByThisWeek = (bookingDate: string) => {
  const bookingDateObj = new Date(bookingDate)
  const now = new Date()
  const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()))
  const endOfWeek = new Date(now.setDate(now.getDate() + 6))

  return bookingDateObj >= startOfWeek && bookingDateObj <= endOfWeek
}

/**
 * Check if booking date is in current month
 * @param bookingDate - Booking date string
 * @returns Whether date is in current month
 */
const filterByThisMonth = (bookingDate: string) => {
  const bookingDateObj = new Date(bookingDate)
  const now = new Date()

  return (
    bookingDateObj.getMonth() === now.getMonth()
    && bookingDateObj.getFullYear() === now.getFullYear()
  )
}

/**
 * Apply all filters to a booking
 * @param booking - Booking item
 * @param filters - All filter criteria
 * @returns Whether booking passes all filters
 */
export const applyAllFilters = (booking: AdminBookingItem, filters: BookingFilters) => {
  return (
    filterByTab(booking, filters.bookingTab)
    && filterBySearch(booking, filters.searchQuery)
    && filterByStatus(booking, filters.statusFilter)
    && filterByDate(booking, filters.dateFilter)
  )
}
