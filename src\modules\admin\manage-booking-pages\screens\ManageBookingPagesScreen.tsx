'use client'

import { But<PERSON> } from '@/components/ui/button'
import { appPaths } from '@/utils/app-routes'
import { Plus } from 'lucide-react'
import Link from 'next/link'
import React from 'react'
import BookingPageFilters from '../components/BookingPageFilters'
import BookingPageList from '../components/BookingPageList'
import useBookingPages from '../hooks/useBookingPages'

/**
 * Screen component for managing booking pages
 */
const ManageBookingPagesScreen = () => {
  const {
    bookingPages,
    isLoading,
    error,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    currentPage,
    setCurrentPage,
    totalPages,
    fetchBookingPages,
    hasFilters,
  } = useBookingPages()

  // Không cần state cho control panel vì đã chuyển sang trang riêng

  // Handlers
  const handleEditAction = (id: string) => {
    // Điều hướng đến trang edit
    window.location.href = `/admin/manage-booking-pages/${id}/edit`
  }

  const handleViewStatsAction = (id: string) => {
    console.warn(`View stats for booking page with ID: ${id}`)
    // Trong thực tế sẽ điều hướng đến trang thống kê
    // window.location.href = `/admin/booking-page-stats/${id}`
  }

  const handleCreateNew = () => {
    window.location.href = '/admin/create-booking-page'
  }

  const handleViewLiveAction = (id: string, slug: string) => {
    console.warn(`View live booking page with ID: ${id}, slug: ${slug}`)
    // Trong thực tế sẽ mở trang booking trong tab mới
    window.open(`/${slug}`, '_blank')
  }

  const handleOpenControlPanel = (id: string) => {
    // Chuyển hướng đến trang quản lý booking page
    window.location.href = appPaths.admin.bookingPagePanel(id)
  }

  // Không cần hàm đóng control panel vì đã chuyển sang trang riêng

  return (
    <div className="max-w-5xl mx-auto py-8 px-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl font-bold">Quản lý Booking Pages</h1>
        <Link href="/admin/create-booking-page">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Tạo mới
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <BookingPageFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
      />

      {/* Results */}
      <BookingPageList
        bookingPages={bookingPages}
        isLoading={isLoading}
        error={error}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
        onRetry={fetchBookingPages}
        onEditAction={handleEditAction}
        onViewStatsAction={handleViewStatsAction}
        onViewLiveAction={handleViewLiveAction}
        onCreateNew={handleCreateNew}
        onOpenControlPanel={handleOpenControlPanel}
        hasFilters={hasFilters}
      />

      {/* Không cần control panel ở đây vì đã chuyển sang trang riêng */}
    </div>
  )
}

export default ManageBookingPagesScreen
