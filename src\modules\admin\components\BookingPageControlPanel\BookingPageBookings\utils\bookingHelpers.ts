import type { AdminBookingItem } from '@/modules/admin/apis/booking-page.api'
import type { FormattedBookingSlotsData } from '../types'
import { getFieldName } from '@/utils/field-booking'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'

/**
 * Format booking slots display by grouping them by date
 * @param slots - Array of booking slots
 * @returns Formatted slots data with dates and slot info
 */
export const formatBookingSlots = (slots: AdminBookingItem['bookingSlots']): FormattedBookingSlotsData => {
  if (!slots || slots.length === 0) {
    return { dates: [], slotsInfo: 'Không có slot' }
  }

  // Group slots by date
  const slotsByDate = slots.reduce((acc, slot) => {
    const date = slot.date
    if (!acc[date]) {
      acc[date] = []
    }
    acc[date].push(slot)
    return acc
  }, {} as Record<string, typeof slots>)

  const dates = Object.keys(slotsByDate).sort()
  const slotsInfo = Object.entries(slotsByDate).map(([date, dateSlots]) => {
    const formattedDate = format(new Date(date), 'dd/MM/yyyy', { locale: vi })
    const fieldTimes = dateSlots.map((slot) => {
      const fieldDisplayName = (slot as any).fieldName ?? getFieldName(slot.field)
      return `${fieldDisplayName} (${slot.time})`
    }).join(', ')
    return { date: formattedDate, fieldTimes }
  })

  return { dates, slotsInfo }
}

/**
 * Get badge variant based on booking status
 * @param status - Booking status
 * @returns Badge variant
 */
export const getStatusBadgeVariant = (status: AdminBookingItem['status']) => {
  switch (status) {
    case 'confirmed':
      return 'default'
    case 'pending':
      return 'outline'
    case 'cancelled':
      return 'destructive'
    default:
      return 'outline'
  }
}

/**
 * Get status display text in Vietnamese
 * @param status - Booking status
 * @returns Vietnamese status text
 */
export const getStatusDisplayText = (status: AdminBookingItem['status']) => {
  switch (status) {
    case 'confirmed':
      return 'Đã xác nhận'
    case 'pending':
      return 'Chờ xác nhận'
    case 'cancelled':
      return 'Đã hủy'
    default:
      return status
  }
}

/**
 * Get payment method display text in Vietnamese
 * @param paymentMethod - Payment method
 * @returns Vietnamese payment method text
 */
export const getPaymentMethodDisplayText = (paymentMethod: string) => {
  switch (paymentMethod) {
    case 'COD':
      return 'Thanh toán khi nhận'
    default:
      return paymentMethod
  }
}

/**
 * Format date for display
 * @param date - Date string or Date object
 * @returns Formatted date string
 */
export const formatDisplayDate = (date: string | Date) => {
  return format(new Date(date), 'dd/MM/yyyy', { locale: vi })
}

/**
 * Format datetime for display
 * @param date - Date string or Date object
 * @returns Formatted datetime string
 */
export const formatDisplayDateTime = (date: string | Date) => {
  return format(new Date(date), 'dd/MM/yyyy HH:mm', { locale: vi })
}
