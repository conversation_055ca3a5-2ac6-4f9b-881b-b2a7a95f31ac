'use client'

import type { FieldType } from '../../types'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import {
  Clock,
  Image as ImageIcon,
  Plus,
  Settings,
  Trash2,
  Users,
} from 'lucide-react'
import React from 'react'
import { useCreateBookingPageV2Store } from '../../stores/create-booking-page-v2.store'

const FIELD_TYPES: { value: FieldType, label: string }[] = [
  { value: 'football', label: 'Bóng đá' },
  { value: 'tennis', label: 'Tennis' },
  { value: 'badminton', label: 'Cầu lông' },
  { value: 'basketball', label: 'Bóng rổ' },
]

export const BookingConfigStep: React.FC = () => {
  const [previewMode, setPreviewMode] = React.useState<'desktop' | 'mobile'>('desktop')

  const {
    pageInfo,
    bookingConfig,
    updateBookingConfig,
    addField,
    removeField,
    updateField,
    errors,
  } = useCreateBookingPageV2Store()

  return (
    <div className="animate-in fade-in slide-in-from-right-4 duration-300">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 flex items-center justify-center gap-3 mb-4">
            <Settings className="w-7 h-7 lg:w-8 lg:h-8 text-orange-500" />
            Cấu hình trang đặt chỗ
          </h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
            Thiết lập thông tin và xem trước giao diện trang đặt chỗ của bạn
          </p>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
          {/* Configuration Panel */}
          <Card className="bg-white/90 backdrop-blur-md border-orange-200/50 shadow-2xl">
            <CardHeader className="px-6 py-6">
              <CardTitle className="text-xl text-gray-900">Cấu hình chi tiết</CardTitle>
              <p className="text-gray-600">
                Thiết lập thông tin cơ bản để trang đặt chỗ có thể hoạt động
              </p>
            </CardHeader>

            <CardContent className="px-6 sm:px-8 lg:px-12 pb-8 space-y-8">
              {/* Banner Configuration */}
              <div className="space-y-6">
                <h3 className="text-lg lg:text-xl font-semibold text-gray-900 flex items-center gap-3">
                  <ImageIcon className="w-6 h-6 text-orange-500" />
                  Thông tin banner
                </h3>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <Label htmlFor="bannerTitle" className="text-base font-semibold text-gray-800">
                      Tiêu đề banner *
                    </Label>
                    <Input
                      id="bannerTitle"
                      value={bookingConfig.bannerTitle}
                      onChange={e => updateBookingConfig({ bannerTitle: e.target.value })}
                      placeholder="Đặt sân thể thao ABC"
                      className={`h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                        errors.bannerTitle ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                      }`}
                    />
                    {errors.bannerTitle && (
                      <p className="text-sm text-red-600">{errors.bannerTitle}</p>
                    )}
                  </div>

                  <div className="space-y-3">
                    <Label htmlFor="bannerSubtitle" className="text-base font-semibold text-gray-800">
                      Mô tả ngắn
                    </Label>
                    <Input
                      id="bannerSubtitle"
                      value={bookingConfig.bannerSubtitle}
                      onChange={e => updateBookingConfig({ bannerSubtitle: e.target.value })}
                      placeholder="Chọn sân và thời gian phù hợp với bạn"
                      className={`h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                        errors.bannerSubtitle ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                      }`}
                    />
                    {errors.bannerSubtitle && (
                      <p className="text-sm text-red-600">{errors.bannerSubtitle}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="bannerImage" className="text-base font-semibold text-gray-800">
                    URL hình ảnh banner
                  </Label>
                  <Input
                    id="bannerImage"
                    value={bookingConfig.bannerImage}
                    onChange={e => updateBookingConfig({ bannerImage: e.target.value })}
                    placeholder="https://example.com/banner.jpg"
                    className={`h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                      errors.bannerImage ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                    }`}
                  />
                  {errors.bannerImage && (
                    <p className="text-sm text-red-600">{errors.bannerImage}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    Hỗ trợ định dạng: JPG, JPEG, PNG, GIF, WebP
                  </p>
                </div>
              </div>

              <Separator className="bg-orange-200" />

              {/* Operating Hours */}
              <div className="space-y-6">
                <h3 className="text-lg lg:text-xl font-semibold text-gray-900 flex items-center gap-3">
                  <Clock className="w-6 h-6 text-orange-500" />
                  Giờ hoạt động
                </h3>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <Label htmlFor="openTime" className="text-base font-semibold text-gray-800">
                      Giờ mở cửa *
                    </Label>
                    <Input
                      id="openTime"
                      type="time"
                      value={bookingConfig.openTime}
                      onChange={e => updateBookingConfig({ openTime: e.target.value })}
                      className={`h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                        errors.openTime ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                      }`}
                    />
                    {errors.openTime && (
                      <p className="text-sm text-red-600">{errors.openTime}</p>
                    )}
                  </div>

                  <div className="space-y-3">
                    <Label htmlFor="closeTime" className="text-base font-semibold text-gray-800">
                      Giờ đóng cửa *
                    </Label>
                    <Input
                      id="closeTime"
                      type="time"
                      value={bookingConfig.closeTime}
                      onChange={e => updateBookingConfig({ closeTime: e.target.value })}
                      className={`h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                        errors.closeTime ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                      }`}
                    />
                    {errors.closeTime && (
                      <p className="text-sm text-red-600">{errors.closeTime}</p>
                    )}
                  </div>
                </div>
              </div>

              <Separator className="bg-orange-200" />

              {/* Fields Configuration */}
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg lg:text-xl font-semibold text-gray-900 flex items-center gap-3">
                    <Users className="w-6 h-6 text-orange-500" />
                    Danh sách sân (
                    {bookingConfig.fields.length}
                    )
                  </h3>
                  <Button
                    onClick={addField}
                    variant="outline"
                    size="sm"
                    className="border-orange-300 text-orange-600 hover:bg-orange-50 h-10 px-4"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Thêm sân
                  </Button>
                </div>

                {errors.fields && (
                  <p className="text-sm text-red-600">{errors.fields}</p>
                )}

                {bookingConfig.fields.length === 0
                  ? (
                      <div className="text-center py-12 text-gray-500 border-2 border-dashed border-gray-300 rounded-xl">
                        <Users className="w-16 h-16 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium mb-2">Chưa có sân nào</p>
                        <p className="text-sm">Nhấn "Thêm sân" để bắt đầu thêm sân đầu tiên</p>
                      </div>
                    )
                  : (
                      <div className="space-y-4">
                        {bookingConfig.fields.map((field, index) => (
                          <div
                            key={field.id}
                            className="p-6 border border-orange-200 rounded-xl bg-gradient-to-r from-orange-50/50 to-orange-100/50"
                          >
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                              <div className="space-y-2">
                                <Label className="text-sm font-semibold text-gray-800">
                                  Tên sân *
                                </Label>
                                <Input
                                  value={field.name}
                                  onChange={e => updateField(field.id, { name: e.target.value })}
                                  placeholder={`Sân ${index + 1}`}
                                  className="h-10 border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-1"
                                />
                              </div>

                              <div className="space-y-2">
                                <Label className="text-sm font-semibold text-gray-800">
                                  Loại sân
                                </Label>
                                <select
                                  value={field.type}
                                  onChange={e => updateField(field.id, { type: e.target.value as FieldType })}
                                  className="w-full h-10 px-3 py-2 border border-orange-200 rounded-md focus:border-orange-400 focus:ring-orange-400 focus:ring-1 bg-white"
                                >
                                  {FIELD_TYPES.map(type => (
                                    <option key={type.value} value={type.value}>
                                      {type.label}
                                    </option>
                                  ))}
                                </select>
                              </div>

                              <div className="space-y-2">
                                <Label className="text-sm font-semibold text-gray-800">
                                  Sức chứa
                                </Label>
                                <Input
                                  type="number"
                                  min="1"
                                  max="100"
                                  value={field.capacity}
                                  onChange={e => updateField(field.id, { capacity: Number.parseInt(e.target.value) || 1 })}
                                  className="h-10 border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-1"
                                />
                              </div>

                              <div className="flex justify-end">
                                <Button
                                  onClick={() => removeField(field.id)}
                                  variant="outline"
                                  size="sm"
                                  className="border-red-300 text-red-600 hover:bg-red-50 h-10 w-10 p-0"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
              </div>
            </CardContent>
          </Card>

          {/* Preview Panel */}
          <div className="xl:sticky xl:top-6">
            <Card className="bg-white/90 backdrop-blur-md border-orange-200/50 shadow-2xl">
              <CardHeader className="px-6 py-6">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl text-gray-900">Xem trước giao diện</CardTitle>
                    <p className="text-gray-600">
                      Giao diện trang đặt chỗ sẽ hiển thị như thế này
                    </p>
                  </div>
                  <div className="flex items-center gap-3">
                    {/* Responsive Toggle */}
                    <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
                      <Button
                        type="button"
                        variant={previewMode === 'desktop' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setPreviewMode('desktop')}
                        className="h-8 w-8 p-0"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                          <line x1="8" y1="21" x2="16" y2="21" />
                          <line x1="12" y1="17" x2="12" y2="21" />
                        </svg>
                      </Button>
                      <Button
                        type="button"
                        variant={previewMode === 'mobile' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setPreviewMode('mobile')}
                        className="h-8 w-8 p-0"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <rect x="5" y="2" width="14" height="20" rx="2" ry="2" />
                          <line x1="12" y1="18" x2="12.01" y2="18" />
                        </svg>
                      </Button>
                    </div>
                    {/* Browser Dots */}
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-400"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                      <div className="w-3 h-3 rounded-full bg-green-400"></div>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="px-6 pb-6">
                <div className={`bg-gray-50 rounded-xl p-4 space-y-6 max-h-[600px] overflow-y-auto transition-all duration-300 ${
                  previewMode === 'mobile' ? 'max-w-sm mx-auto' : 'w-full'
                }`}
                >
                  {/* Preview Header with Page Info */}
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="text-center">
                      <h2 className={`font-bold text-gray-900 mb-2 ${
                        previewMode === 'mobile' ? 'text-lg' : 'text-xl'
                      }`}
                      >
                        {pageInfo.name || 'Tên trang đặt chỗ'}
                      </h2>
                      <p className={`text-gray-600 ${
                        previewMode === 'mobile' ? 'text-xs' : 'text-sm'
                      }`}
                      >
                        {pageInfo.description || 'Mô tả trang đặt chỗ sẽ hiển thị ở đây'}
                      </p>
                      {pageInfo.slug && (
                        <p className="text-xs text-orange-600 mt-2 font-mono">
                          booking-easy.com/
                          {pageInfo.slug}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Preview Banner */}
                  <div className="relative">
                    {bookingConfig.bannerImage
                      ? (
                          <div
                            className={`bg-cover bg-center rounded-lg relative overflow-hidden ${
                              previewMode === 'mobile' ? 'h-32' : 'h-48'
                            }`}
                            style={{ backgroundImage: `url(${bookingConfig.bannerImage})` }}
                          >
                            <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                              <div className="text-center text-white">
                                <h1 className={`font-bold mb-2 ${
                                  previewMode === 'mobile' ? 'text-lg' : 'text-2xl lg:text-3xl'
                                }`}
                                >
                                  {bookingConfig.bannerTitle || 'Tiêu đề banner'}
                                </h1>
                                {bookingConfig.bannerSubtitle && (
                                  <p className={`opacity-90 ${
                                    previewMode === 'mobile' ? 'text-sm' : 'text-lg'
                                  }`}
                                  >
                                    {bookingConfig.bannerSubtitle}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        )
                      : (
                          <div className={`bg-gradient-to-r from-orange-400 to-orange-600 rounded-lg flex items-center justify-center ${
                            previewMode === 'mobile' ? 'h-32' : 'h-48'
                          }`}
                          >
                            <div className="text-center text-white">
                              <h1 className={`font-bold mb-2 ${
                                previewMode === 'mobile' ? 'text-lg' : 'text-2xl lg:text-3xl'
                              }`}
                              >
                                {bookingConfig.bannerTitle || 'Tiêu đề banner'}
                              </h1>
                              {bookingConfig.bannerSubtitle && (
                                <p className={`opacity-90 ${
                                  previewMode === 'mobile' ? 'text-sm' : 'text-lg'
                                }`}
                                >
                                  {bookingConfig.bannerSubtitle}
                                </p>
                              )}
                            </div>
                          </div>
                        )}
                  </div>

                  {/* Preview Operating Hours */}
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Clock className="w-5 h-5 text-orange-500" />
                      Giờ hoạt động
                    </h3>
                    <div className="text-gray-700">
                      {bookingConfig.openTime && bookingConfig.closeTime
                        ? (
                            <p>
                              Mở cửa:
                              {' '}
                              <span className="font-medium">{bookingConfig.openTime}</span>
                              {' '}
                              -
                              Đóng cửa:
                              {' '}
                              <span className="font-medium">{bookingConfig.closeTime}</span>
                            </p>
                          )
                        : (
                            <p className="text-gray-500 italic">Chưa thiết lập giờ hoạt động</p>
                          )}
                    </div>
                  </div>

                  {/* Preview Fields */}
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Users className="w-5 h-5 text-orange-500" />
                      Danh sách sân (
                      {bookingConfig.fields.length}
                      )
                    </h3>

                    {bookingConfig.fields.length === 0
                      ? (
                          <p className="text-gray-500 italic text-center py-8">
                            Chưa có sân nào được thêm
                          </p>
                        )
                      : (
                          <div className={`grid gap-3 ${
                            previewMode === 'mobile' ? 'grid-cols-1' : 'grid-cols-1 sm:grid-cols-2'
                          }`}
                          >
                            {bookingConfig.fields.map(field => (
                              <div
                                key={field.id}
                                className="bg-gradient-to-r from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-3"
                              >
                                <div className="flex items-center justify-between">
                                  <div>
                                    <h4 className="font-medium text-gray-900">{field.name}</h4>
                                    <p className="text-sm text-gray-600 capitalize">
                                      {field.type === 'football' && '⚽ Bóng đá'}
                                      {field.type === 'tennis' && '🎾 Tennis'}
                                      {field.type === 'badminton' && '🏸 Cầu lông'}
                                      {field.type === 'basketball' && '🏀 Bóng rổ'}
                                    </p>
                                  </div>
                                  <div className="text-right">
                                    <p className="text-sm font-medium text-orange-600">
                                      {field.capacity}
                                      {' '}
                                      người
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                  </div>

                  {/* Preview Booking Button */}
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="text-center">
                      <button
                        type="button"
                        className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-8 rounded-lg shadow-lg transition-colors"
                      >
                        Đặt sân ngay
                      </button>
                      <p className="text-sm text-gray-500 mt-2">
                        Nút đặt chỗ sẽ hiển thị như thế này
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
