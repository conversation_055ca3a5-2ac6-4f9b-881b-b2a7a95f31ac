import type { BookingField, FieldType } from '../types'

// Generate unique field ID
export const generateFieldId = (): string => {
  return `field-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// Create default field
export const createDefaultField = (index: number): BookingField => {
  return {
    id: generateFieldId(),
    name: `Sân ${index + 1}`,
    type: 'football' as FieldType,
    capacity: 1,
  }
}

// Format time for display
export const formatTime = (time: string): string => {
  if (!time) {
    return ''
  }

  try {
    const [hours, minutes] = time.split(':')
    const hour = Number.parseInt(hours, 10)
    const minute = Number.parseInt(minutes, 10)

    if (isNaN(hour) || isNaN(minute)) {
      return time
    }

    const period = hour >= 12 ? 'PM' : 'AM'
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour

    return `${displayHour}:${minute.toString().padStart(2, '0')} ${period}`
  } catch {
    return time
  }
}

// Calculate duration between two times
export const calculateDuration = (startTime: string, endTime: string): string => {
  if (!startTime || !endTime) {
    return ''
  }

  try {
    const [startHours, startMinutes] = startTime.split(':').map(Number)
    const [endHours, endMinutes] = endTime.split(':').map(Number)

    const startTotalMinutes = startHours * 60 + startMinutes
    const endTotalMinutes = endHours * 60 + endMinutes

    let durationMinutes = endTotalMinutes - startTotalMinutes

    // Handle next day scenario
    if (durationMinutes < 0) {
      durationMinutes += 24 * 60
    }

    const hours = Math.floor(durationMinutes / 60)
    const minutes = durationMinutes % 60

    if (hours === 0) {
      return `${minutes} phút`
    } else if (minutes === 0) {
      return `${hours} giờ`
    } else {
      return `${hours} giờ ${minutes} phút`
    }
  } catch {
    return ''
  }
}

// Generate subdomain suggestions
export const generateSubdomainSuggestions = (baseName: string): string[] => {
  if (!baseName) {
    return []
  }

  const cleanName = baseName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')

  const suggestions = [
    cleanName,
    `${cleanName}-booking`,
    `${cleanName}-sport`,
    `${cleanName}-center`,
    `${cleanName}-club`,
    `book-${cleanName}`,
    `${cleanName}-online`,
    `${cleanName}-reserve`,
  ]

  return suggestions.filter(s => s.length >= 3 && s.length <= 50)
}

// Validate field configuration
export const validateFieldConfig = (fields: BookingField[]): string[] => {
  const errors: string[] = []

  if (fields.length === 0) {
    errors.push('Cần có ít nhất một sân')
    return errors
  }

  // Check for duplicate names
  const names = fields.map(f => f.name.toLowerCase().trim())
  const duplicates = names.filter((name, index) => names.indexOf(name) !== index)

  if (duplicates.length > 0) {
    errors.push('Tên sân không được trùng lặp')
  }

  // Check individual fields
  fields.forEach((field, index) => {
    if (!field.name || field.name.trim().length < 2) {
      errors.push(`Sân ${index + 1}: Tên sân phải có ít nhất 2 ký tự`)
    }

    if (field.capacity < 1 || field.capacity > 100) {
      errors.push(`Sân ${index + 1}: Sức chứa phải từ 1 đến 100`)
    }
  })

  return errors
}

// Get field type display name
export const getFieldTypeDisplayName = (type: FieldType): string => {
  const typeNames: Record<FieldType, string> = {
    football: 'Bóng đá',
    tennis: 'Tennis',
    badminton: 'Cầu lông',
    basketball: 'Bóng rổ',
  }

  return typeNames[type] || type
}

// Generate preview URL
export const generatePreviewUrl = (subdomain: string): string => {
  if (!subdomain) {
    return ''
  }
  return `https://${subdomain}.booking-easy.com`
}

// Check if subdomain is available (mock function)
export const checkSubdomainAvailability = async (subdomain: string): Promise<boolean> => {
  // TODO: Implement actual API call
  await new Promise(resolve => setTimeout(resolve, 500))

  // Mock some unavailable subdomains
  const unavailable = ['admin', 'api', 'www', 'test', 'demo', 'app']
  return !unavailable.includes(subdomain.toLowerCase())
}

// Format validation errors for display
export const formatValidationError = (error: string): string => {
  // Capitalize first letter and ensure proper punctuation
  const formatted = error.charAt(0).toUpperCase() + error.slice(1)
  return formatted.endsWith('.') ? formatted : `${formatted}.`
}
