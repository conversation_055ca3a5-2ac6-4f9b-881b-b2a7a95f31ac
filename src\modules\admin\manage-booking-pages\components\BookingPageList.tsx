import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { BookingPageCard } from '@/modules/admin/components/BookingPageCard'
import { Alert<PERSON>ircle, RefreshCw } from 'lucide-react'
import React from 'react'
import BookingPageCardSkeleton from './BookingPageCardSkeleton'
import BookingPagePagination from './BookingPagePagination'
import EmptyState from './EmptyState'

interface BookingPageListProps {
  /** List of booking pages to display */
  bookingPages: (BookingPageItem | any)[]
  /** Whether data is loading */
  isLoading: boolean
  /** Error message if any */
  error: string | null
  /** Current page number */
  currentPage: number
  /** Total number of pages */
  totalPages: number
  /** Handler for page change */
  onPageChange: (page: number) => void
  /** Handler for retry button */
  onRetry: () => void
  /** Handler for edit action */
  onEditAction: (id: string) => void
  /** Handler for view stats action */
  onViewStatsAction: (id: string) => void
  /** Handler for view live page action */
  onViewLiveAction: (id: string, slug: string) => void
  /** Handler for create new button */
  onCreateNew: () => void
  /** Handler for opening the control panel */
  onOpenControlPanel?: (id: string) => void
  /** Whether filters are applied */
  hasFilters: boolean
}

/**
 * Component to display a list of booking pages with loading, error and empty states
 */
export const BookingPageList = ({
  bookingPages,
  isLoading,
  error,
  currentPage,
  totalPages,
  onPageChange,
  onRetry,
  onEditAction,
  onViewStatsAction,
  onViewLiveAction,
  onCreateNew,
  onOpenControlPanel,
  hasFilters,
}: BookingPageListProps) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <BookingPageCardSkeleton />
        <BookingPageCardSkeleton />
        <BookingPageCardSkeleton />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Lỗi</AlertTitle>
        <AlertDescription>
          {error}
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="mt-2 ml-2"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Thử lại
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  if (bookingPages.length === 0) {
    return (
      <EmptyState
        hasFilters={hasFilters}
        onCreateNew={onCreateNew}
      />
    )
  }

  return (
    <div className="space-y-4">
      {bookingPages.map(page => (
        <BookingPageCard
          key={page.id}
          item={{
            ...page,
            template: page.templateCode || page.template || '',
            avatar: page.avatar || (page.name ? `https://placehold.co/100x100?text=${encodeURIComponent(page.name[0])}` : 'https://placehold.co/100x100?text=BP'),
            views: page.views || 0,
            bookings: page.bookings || 0,
            slug: page.slug || '',
          }}
          onEditAction={onEditAction}
          onViewStatsAction={onViewStatsAction}
          onViewLiveAction={onViewLiveAction}
          onOpenControlPanel={onOpenControlPanel}
        />
      ))}

      <BookingPagePagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
      />
    </div>
  )
}

export default BookingPageList
