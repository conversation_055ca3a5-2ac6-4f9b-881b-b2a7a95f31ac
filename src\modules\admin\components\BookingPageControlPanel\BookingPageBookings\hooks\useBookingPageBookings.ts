import type { AdminBookingItem } from '@/modules/admin/apis/booking-page.api'
import type { PaginationControls } from '../types'
import { bookingPageAPIs } from '@/modules/admin/apis/booking-page.api'
import { useCallback, useEffect, useState } from 'react'
import { toast } from 'sonner'

interface UseBookingPageBookingsParams {
  bookingPageId: string
  status?: string
  startDate?: string
  endDate?: string
  initialPageSize?: number
}

interface UseBookingPageBookingsReturn {
  bookings: AdminBookingItem[]
  isLoading: boolean
  error: string | null
  pagination: PaginationControls
  fetchBookings: () => Promise<void>
  refetch: () => Promise<void>
}

/**
 * Custom hook to fetch and manage booking page bookings
 */
export const useBookingPageBookings = (params: UseBookingPageBookingsParams): UseBookingPageBookingsReturn => {
  const [bookings, setBookings] = useState<AdminBookingItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(params.initialPageSize || 20)
  const [total, setTotal] = useState(0)

  // Calculate pagination values
  const totalPages = Math.ceil(total / pageSize)
  const offset = (currentPage - 1) * pageSize

  // Pagination controls
  const pagination: PaginationControls = {
    currentPage,
    pageSize,
    total,
    totalPages,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
    goToPage: (page: number) => {
      if (page >= 1 && page <= totalPages) {
        setCurrentPage(page)
      }
    },
    goToNextPage: () => {
      if (currentPage < totalPages) {
        setCurrentPage(currentPage + 1)
      }
    },
    goToPreviousPage: () => {
      if (currentPage > 1) {
        setCurrentPage(currentPage - 1)
      }
    },
    changePageSize: (size: number) => {
      setPageSize(size)
      setCurrentPage(1) // Reset to first page when changing page size
    },
  }

  // Fetch bookings from API
  const fetchBookings = useCallback(async () => {
    if (!params.bookingPageId) {
      setBookings([])
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const apiParams = {
        status: params.status,
        startDate: params.startDate,
        endDate: params.endDate,
        limit: pageSize,
        offset,
      }

      // Remove undefined values
      const cleanParams = Object.fromEntries(
        Object.entries(apiParams).filter(([_, value]) => value !== undefined),
      )

      const response = await bookingPageAPIs.getBookingPageBookings(params.bookingPageId, cleanParams)

      if (response && response.status?.success && response.data) {
        const bookingsData = Array.isArray(response.data)
          ? response.data
          : Array.isArray(response.data) ? response.data : []

        setBookings(bookingsData)
        setTotal(response.meta?.total || response.meta?.total || 0)
      } else {
        setBookings([])
        setTotal(0)
        setError('Không thể tải danh sách đặt lịch')
      }
    } catch (err) {
      console.error('Failed to fetch bookings:', err)
      setError('Không thể tải danh sách đặt lịch. Vui lòng thử lại sau.')
      setBookings([])
      setTotal(0)
      toast.error('Không thể tải danh sách đặt lịch')
    } finally {
      setIsLoading(false)
    }
  }, [params.bookingPageId, params.status, params.startDate, params.endDate, pageSize, offset])

  // Refetch function (alias for fetchBookings)
  const refetch = useCallback(() => {
    return fetchBookings()
  }, [fetchBookings])

  // Load bookings on mount and when params change
  useEffect(() => {
    fetchBookings()
  }, [fetchBookings])

  return {
    bookings,
    isLoading,
    error,
    pagination,
    fetchBookings,
    refetch,
  }
}

export default useBookingPageBookings
