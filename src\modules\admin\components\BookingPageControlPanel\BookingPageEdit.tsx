'use client'

import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { TEMPLATE_NAMES } from '@/modules/admin/constants/mock-data'
import { Image as ImageIcon, Save } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { toast } from 'sonner'

interface BookingPageEditProps {
  bookingPageId: string
  bookingPage?: BookingPageItem
  onUpdate?: (updatedData: Partial<BookingPageItem>) => Promise<boolean>
}

/**
 * Edit tab for the booking page control panel
 * Allows editing the booking page configuration
 */
const BookingPageEdit: React.FC<BookingPageEditProps> = ({ bookingPage, onUpdate }) => {
  const [editTab, setEditTab] = useState('basic')
  const [isSaving, setIsSaving] = useState(false)

  // Khởi tạo formData từ bookingPage hoặc sử dụng dữ liệu mặc định
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    slug: '',
    status: 'active' as 'active' | 'inactive' | 'pending',
    template: '',
    templateCode: '',
    avatar: '',
    isPublic: true,
    requireApproval: true,
    allowCancellation: true,
    cancellationTimeLimit: 24, // hours
    maxBookingsPerDay: 10,
    businessHours: {
      start: '06:00',
      end: '22:00',
      daysOfWeek: [1, 2, 3, 4, 5, 6, 0],
    },
    timeSlotInterval: 60, // minutes
    pricePerHour: 300000,
  })

  // Cập nhật formData khi bookingPage thay đổi
  useEffect(() => {
    if (bookingPage) {
      setFormData(prev => ({
        ...prev,
        name: bookingPage.name || prev.name,
        description: bookingPage.description || prev.description,
        slug: bookingPage.slug || prev.slug,
        status: bookingPage.status || prev.status,
        template: bookingPage.templateCode || prev.template,
        templateCode: bookingPage.templateCode || prev.templateCode,
        avatar: (bookingPage as any).avatar || prev.avatar,
        // Các trường khác giữ nguyên giá trị mặc định nếu không có trong bookingPage
      }))
    }
  }, [bookingPage])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleBusinessHoursChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      businessHours: {
        ...prev.businessHours,
        [field]: value,
      },
    }))
  }

  const handleDayToggle = (day: number) => {
    const newDays = formData.businessHours.daysOfWeek.includes(day)
      ? formData.businessHours.daysOfWeek.filter(d => d !== day)
      : [...formData.businessHours.daysOfWeek, day]

    handleBusinessHoursChange('daysOfWeek', newDays)
  }

  const handleSave = async () => {
    if (!onUpdate) {
      toast.error('Không thể cập nhật booking page')
      return
    }

    try {
      setIsSaving(true)

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<BookingPageItem> = {
        name: formData.name,
        description: formData.description,
        slug: formData.slug,
        status: formData.status === 'pending' ? 'active' : formData.status, // Chỉ chấp nhận 'active' hoặc 'inactive'
        templateCode: formData.templateCode || formData.template,
      }

      // Gọi API cập nhật
      const success = await onUpdate(updateData)

      if (success) {
        toast.success('Cập nhật thành công')
      } else {
        toast.error('Không thể cập nhật booking page')
      }
    } catch (error) {
      console.error('Failed to update booking page:', error)
      toast.error('Đã xảy ra lỗi khi cập nhật')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Chỉnh sửa cấu hình</h2>
        <Button
          onClick={handleSave}
          className="flex items-center gap-1"
          disabled={isSaving}
        >
          {isSaving
            ? (
                <>
                  <span className="animate-spin mr-1">◌</span>
                  Đang lưu...
                </>
              )
            : (
                <>
                  <Save className="h-4 w-4" />
                  Lưu thay đổi
                </>
              )}
        </Button>
      </div>

      <Tabs value={editTab} onValueChange={setEditTab}>
        <TabsList className="grid grid-cols-2 w-full md:w-[400px]">
          <TabsTrigger value="basic">Thông tin cơ bản</TabsTrigger>
          <TabsTrigger value="availability">Lịch mở cửa</TabsTrigger>
          {/* <TabsTrigger value="pricing">Giá & Thanh toán</TabsTrigger> */}
        </TabsList>

        <TabsContent value="basic" className="mt-6 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Thông tin cơ bản</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Tên trang booking</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={e => handleInputChange('name', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="slug">Đường dẫn (slug)</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={e => handleInputChange('slug', e.target.value)}
                  />
                  <p className="text-xs text-gray-500">
                    URL: https://pickslot.app/
                    {formData.slug}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Mô tả</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={e => handleInputChange('description', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Trạng thái</Label>
                  <Select
                    value={formData.status}
                    onValueChange={value => handleInputChange('status', value)}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Chọn trạng thái" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Hoạt động</SelectItem>
                      <SelectItem value="inactive">Tạm ngưng</SelectItem>
                      <SelectItem value="pending">Chờ duyệt</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="template">Mẫu giao diện</Label>
                  <Select
                    value={formData.template}
                    onValueChange={value => handleInputChange('template', value)}
                  >
                    <SelectTrigger id="template">
                      <SelectValue placeholder="Chọn mẫu giao diện" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(TEMPLATE_NAMES).map(([key, value]) => (
                        <SelectItem key={key} value={key}>{value}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="avatar">Ảnh đại diện</Label>
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-md border flex items-center justify-center bg-gray-50">
                    {formData.avatar
                      ? (
                          <img src={formData.avatar} alt="Avatar" className="w-full h-full object-cover rounded-md" />
                        )
                      : (
                          <ImageIcon className="h-8 w-8 text-gray-400" />
                        )}
                  </div>
                  <Button variant="outline" type="button">
                    Chọn ảnh
                  </Button>
                </div>
              </div>

              <div className="space-y-4 pt-2">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="public">Hiển thị công khai</Label>
                    <p className="text-sm text-gray-500">
                      Cho phép mọi người xem trang booking này
                    </p>
                  </div>
                  <Switch
                    id="public"
                    checked={formData.isPublic}
                    onCheckedChange={checked => handleInputChange('isPublic', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="approval">Yêu cầu phê duyệt</Label>
                    <p className="text-sm text-gray-500">
                      Đặt lịch cần được xác nhận trước khi hoàn tất
                    </p>
                  </div>
                  <Switch
                    id="approval"
                    checked={formData.requireApproval}
                    onCheckedChange={checked => handleInputChange('requireApproval', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="availability" className="mt-6 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Lịch mở cửa</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startTime">Giờ mở cửa</Label>
                  <Input
                    id="startTime"
                    type="time"
                    value={formData.businessHours.start}
                    onChange={e => handleBusinessHoursChange('start', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endTime">Giờ đóng cửa</Label>
                  <Input
                    id="endTime"
                    type="time"
                    value={formData.businessHours.end}
                    onChange={e => handleBusinessHoursChange('end', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Ngày mở cửa</Label>
                <div className="flex flex-wrap gap-2">
                  {[
                    { day: 1, label: 'T2' },
                    { day: 2, label: 'T3' },
                    { day: 3, label: 'T4' },
                    { day: 4, label: 'T5' },
                    { day: 5, label: 'T6' },
                    { day: 6, label: 'T7' },
                    { day: 0, label: 'CN' },
                  ].map(({ day, label }) => (
                    <Button
                      key={day}
                      type="button"
                      variant={formData.businessHours.daysOfWeek.includes(day) ? 'default' : 'outline'}
                      className="w-12 h-12"
                      onClick={() => handleDayToggle(day)}
                    >
                      {label}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeSlotInterval">Thời lượng mỗi slot (phút)</Label>
                <Select
                  value={formData.timeSlotInterval.toString()}
                  onValueChange={value => handleInputChange('timeSlotInterval', Number.parseInt(value))}
                >
                  <SelectTrigger id="timeSlotInterval">
                    <SelectValue placeholder="Chọn thời lượng" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 phút</SelectItem>
                    <SelectItem value="30">30 phút</SelectItem>
                    <SelectItem value="60">60 phút</SelectItem>
                    <SelectItem value="90">90 phút</SelectItem>
                    <SelectItem value="120">120 phút</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxBookings">Số lượng đặt tối đa mỗi ngày</Label>
                <Input
                  id="maxBookings"
                  type="number"
                  min="1"
                  value={formData.maxBookingsPerDay}
                  onChange={e => handleInputChange('maxBookingsPerDay', Number.parseInt(e.target.value))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="cancellation">Cho phép hủy đặt lịch</Label>
                  <p className="text-sm text-gray-500">
                    Khách hàng có thể hủy đặt lịch trước thời hạn
                  </p>
                </div>
                <Switch
                  id="cancellation"
                  checked={formData.allowCancellation}
                  onCheckedChange={checked => handleInputChange('allowCancellation', checked)}
                />
              </div>

              {formData.allowCancellation && (
                <div className="space-y-2">
                  <Label htmlFor="cancellationTime">Thời hạn hủy (giờ trước khi bắt đầu)</Label>
                  <Input
                    id="cancellationTime"
                    type="number"
                    min="1"
                    value={formData.cancellationTimeLimit}
                    onChange={e => handleInputChange('cancellationTimeLimit', Number.parseInt(e.target.value))}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pricing" className="mt-6 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Giá & Thanh toán</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="price">Giá cơ bản (VNĐ/giờ)</Label>
                <Input
                  id="price"
                  type="number"
                  min="0"
                  step="10000"
                  value={formData.pricePerHour}
                  onChange={e => handleInputChange('pricePerHour', Number.parseInt(e.target.value))}
                />
              </div>

              <div className="pt-4">
                <Button variant="outline" type="button" className="w-full">
                  Cấu hình giá động theo thời gian
                </Button>
              </div>

              <div className="pt-4">
                <Button variant="outline" type="button" className="w-full">
                  Cấu hình phương thức thanh toán
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default BookingPageEdit
