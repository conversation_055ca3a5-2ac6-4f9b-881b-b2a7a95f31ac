import type { Block, Template } from '../types/blocks'
import { LayoutType } from '../types/theme'
import {
  DEFAULT_CALENDAR_BLOCKS,
  DEFAULT_EVENT_BLOCKS,
} from './defaultBlocks'

/**
 * Legacy interface for backward compatibility
 * @deprecated Use Template from types/blocks.ts instead
 */
export type IBookingPageTemplate = BookingPageTemplate

/**
 * Template category for grouping templates
 */
export enum TemplateCategory {
  EVENT = 'event',
  SPORT = 'sport',
  SERVICE = 'service',
  TRANSPORTATION = 'transportation',
}

/**
 * Extended template interface with additional metadata
 */
export interface BookingPageTemplate extends Template {
  category: TemplateCategory
  tags: string[]
  popularity: number
  isNew?: boolean
  isFeatured?: boolean
  previewImage?: string
  supportedBlocks: Block['type'][]
}

/**
 * Factory function to create a new template
 */
export function createTemplate(
  id: string,
  name: string,
  templateCode: string,
  description: string,
  category: TemplateCategory,
  blocks: Block[],
  options: Partial<BookingPageTemplate> = {},
): BookingPageTemplate {
  return {
    id,
    name,
    templateCode,
    description,
    image: options.image || `https://placehold.co/200x120?text=${id.replace(/-/g, '+')}`,
    blocks,
    category,
    tags: options.tags || [],
    popularity: options.popularity || 0,
    isNew: options.isNew,
    isFeatured: options.isFeatured,
    previewImage: options.previewImage,
    supportedBlocks: options.supportedBlocks || ['banner', 'info', 'description', 'booking_ticket', 'map', 'availability_calendar'],
    theme: options.theme || {
      primaryColor: '#2563eb',
      fontFamily: 'Inter, sans-serif',
      layout: LayoutType.VERTICAL,
    },
  }
}

/**
 * All available templates
 */
export const BOOKING_PAGE_TEMPLATES: BookingPageTemplate[] = [
  // SPORT FIELD TEMPLATE WITH BANNER_SPLIT_MAP LAYOUT
  createTemplate(
    'sport-field-split',
    'Đặt sân thể thao - Banner/30-70/Map',
    'SPORT_FIELD_SPLIT',
    'Giao diện đặt sân với banner ở trên, thông tin 30% và lịch 70% ở giữa, bản đồ ở dưới.',
    TemplateCategory.SPORT,
    [
      {
        type: 'banner',
        data: {
          image: 'https://placehold.co/800x400?text=Sport+Field+Banner',
          title: 'Đặt sân thể thao',
          subtitle: 'Chọn sân và thời gian phù hợp với bạn',
        },
      },
      {
        type: 'description',
        data: {
          time: 'Mở cửa: 06:00 - 22:00',
          location: 'Sân thể thao ABC, Quận 1, TP.HCM',
          slots: 10,
          price: 300000,
        },
      },
      {
        type: 'availability_calendar',
        data: {
          title: 'Đặt sân',
          subtitle: 'Chọn ngày và giờ phù hợp với bạn',
          view: 'month',
          defaultView: 'month',
          businessHours: {
            start: '06:00',
            end: '22:00',
            daysOfWeek: [1, 2, 3, 4, 5, 6, 0],
          },
          showWeekends: true,
          firstDayOfWeek: 1,
          timeSlotInterval: 60,
          showAvailabilityLegend: true,
          fields: [
            {
              id: 'field-1',
              name: 'Sân 1',
              type: 'football',
              capacity: 1,
            },
            {
              id: 'field-2',
              name: 'Sân 2',
              type: 'football',
              capacity: 1,
            },
          ],
          configMode: 'common',
          commonConfig: {
            pricePerHour: 300000,
            dynamicPricing: {
              enabled: false,
              timeBasedPrices: [],
              dayBasedPrices: [],
              combinedRules: [],
            },
          },
        },
      },
      {
        type: 'booking_ticket',
        data: {
          fields: ['name', 'email', 'phone'],
          payment_methods: ['COD'],
        },
      },
      {
        type: 'map',
        data: {
          iframe: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.4946681007846!2d106.69908367469967!3d10.771913089387625!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31752f4b3330bcc7%3A0x4db964d76bf6e18e!2sIndependence%20Palace!5e0!3m2!1sen!2s!4v1689612010894!5m2!1sen!2s',
        },
      },
    ],
    {
      image: 'https://placehold.co/200x120?text=Sport+Field+Split',
      tags: ['sport', 'football', 'tennis', 'split-layout'],
      popularity: 90,
      isNew: true,
      theme: {
        primaryColor: '#10b981',
        fontFamily: 'Inter, sans-serif',
        layout: LayoutType.BANNER_SPLIT_MAP,
      },
    },
  ),
  // SPORT FIELD TEMPLATES WITH BANNER-2COLUMN LAYOUT
  createTemplate(
    'sport-field-banner-2column',
    'Đặt sân thể thao - Banner + 2 cột',
    'SPORT_FIELD_BANNER_2COLUMN',
    'Giao diện đặt sân với banner ở trên và 2 cột thông tin bên dưới.',
    TemplateCategory.SPORT,
    [
      {
        type: 'banner',
        data: {
          image: 'https://placehold.co/800x400?text=Sport+Field+Banner',
          title: 'Đặt sân thể thao',
          subtitle: 'Chọn sân và thời gian phù hợp với bạn',
        },
      },
      {
        type: 'info',
        data: {
          time: 'Mở cửa: 06:00 - 22:00',
          location: 'Sân thể thao ABC, Quận 1, TP.HCM',
          slots: 10,
          price: 300000,
        },
      },
      {
        type: 'availability_calendar',
        data: {
          title: 'Đặt sân',
          subtitle: 'Chọn ngày và giờ phù hợp với bạn',
          view: 'month',
          defaultView: 'month',
          businessHours: {
            start: '06:00',
            end: '22:00',
            daysOfWeek: [1, 2, 3, 4, 5, 6, 0],
          },
          showWeekends: true,
          firstDayOfWeek: 1,
          timeSlotInterval: 60,
          showAvailabilityLegend: true,
          fields: [
            {
              id: 'field-1',
              name: 'Sân 1',
              type: 'football',
              capacity: 1,
            },
            {
              id: 'field-2',
              name: 'Sân 2',
              type: 'football',
              capacity: 1,
            },
          ],
          configMode: 'common',
          commonConfig: {
            pricePerHour: 300000,
            dynamicPricing: {
              enabled: false,
              timeBasedPrices: [],
              dayBasedPrices: [],
              combinedRules: [],
            },
          },
        },
      },
      {
        type: 'booking_ticket',
        data: {
          fields: ['name', 'email', 'phone'],
          payment_methods: ['COD'],
        },
      },
      {
        type: 'map',
        data: {
          iframe: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.4946681007846!2d106.69908367469967!3d10.771913089387625!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31752f4b3330bcc7%3A0x4db964d76bf6e18e!2sIndependence%20Palace!5e0!3m2!1sen!2s!4v1689612010894!5m2!1sen!2s',
        },
      },
    ],
    {
      image: 'https://placehold.co/200x120?text=Sport+Field+Banner+2Column',
      tags: ['sport', 'football', 'tennis', 'banner-2column'],
      popularity: 85,
      isNew: true,
      theme: {
        primaryColor: '#10b981',
        fontFamily: 'Inter, sans-serif',
        layout: LayoutType.SPORT_BASIC,
      },
    },
  ),

  // SERVICE TEMPLATES
  createTemplate(
    'simple-calendar',
    'Đặt sân thể thao - Theo khung giờ',
    'SPORT_FIELD_SLOTS',
    'Chọn ngày, chọn sân, chọn slot thời gian (15"/30"), số người.',
    TemplateCategory.SPORT,
    DEFAULT_CALENDAR_BLOCKS.length > 0
      ? DEFAULT_CALENDAR_BLOCKS
      : [
          {
            type: 'banner',
            data: {
              image: 'https://placehold.co/800x400?text=Simple+Calendar',
              title: 'Đặt lịch dịch vụ',
              subtitle: 'Nhanh chóng và đơn giản',
            },
          },
          {
            type: 'booking_ticket',
            data: {
              fields: ['name', 'email', 'phone', 'date', 'time'],
              payment_methods: ['COD'],
            },
          },
        ],
    {
      image: 'https://placehold.co/200x120?text=Simple+Calendar',
      tags: ['service', 'calendar', 'simple'],
      popularity: 80,
      supportedBlocks: ['banner', 'info', 'description', 'booking_ticket', 'map', 'availability_calendar'],
      theme: {
        primaryColor: '#8b5cf6',
        fontFamily: 'Inter, sans-serif',
        layout: LayoutType.VERTICAL,
      },
    },
  ),
  // EVENT TEMPLATES
  createTemplate(
    'event-booking',
    'Đặt chỗ sự kiện',
    'EVENT_BOOKING',
    'Hỗ trợ đặt chỗ cho sự kiện, số người tham gia.',
    TemplateCategory.EVENT,
    DEFAULT_EVENT_BLOCKS,
    {
      image: 'https://placehold.co/200x120?text=Event+Booking',
      tags: ['event', 'workshop', 'conference'],
      popularity: 95,
      isFeatured: true,
      theme: {
        primaryColor: '#2563eb',
        fontFamily: 'Inter, sans-serif',
        layout: LayoutType.VERTICAL,
      },
    },
  ),

  createTemplate(
    'spa-booking',
    'Đặt lịch Spa',
    'SPA_BOOKING',
    'Template đặt lịch cho các dịch vụ spa, massage, làm đẹp.',
    TemplateCategory.SERVICE,
    [
      {
        type: 'banner',
        data: {
          image: 'https://placehold.co/800x400?text=Spa+Services',
          title: 'Dịch vụ Spa & Làm đẹp',
          subtitle: 'Thư giãn và làm mới bản thân',
        },
      },
      {
        type: 'description',
        data: {
          content: 'Cung cấp các dịch vụ spa cao cấp, massage thư giãn và các liệu pháp làm đẹp chuyên nghiệp.',
        },
      },
      {
        type: 'booking_ticket',
        data: {
          fields: ['name', 'email', 'phone', 'date', 'time', 'service_type'],
          payment_methods: ['COD'],
        },
      },
      {
        type: 'map',
        data: {
          iframe: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.4946681007846!2d106.69908367469967!3d10.771913089387625!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31752f4b3330bcc7%3A0x4db964d76bf6e18e!2sIndependence%20Palace!5e0!3m2!1sen!2s!4v1689612010894!5m2!1sen!2s',
        },
      },
    ],
    {
      tags: ['spa', 'beauty', 'wellness', 'massage'],
      popularity: 75,
      isNew: true,
      theme: {
        primaryColor: '#ec4899',
        fontFamily: 'Montserrat, sans-serif',
        layout: LayoutType.VERTICAL,
      },
    },
  ),

  // TRANSPORTATION TEMPLATES
  createTemplate(
    'car-booking',
    'Đặt xe - Vị trí xe',
    'CAR_BOOKING',
    'Hỗ trợ đặt xe với vị trí xe, thời gian đón, loại xe, điểm đến.',
    TemplateCategory.TRANSPORTATION,
    [
      {
        type: 'banner',
        data: {
          image: 'https://placehold.co/800x400?text=Car+Booking',
          title: 'Dịch vụ đặt xe',
          subtitle: 'Nhanh chóng, tiện lợi, an toàn',
        },
      },
      {
        type: 'description',
        data: {
          content: 'Dịch vụ đặt xe với đội ngũ tài xế chuyên nghiệp, xe đời mới, đảm bảo an toàn và thoải mái.',
        },
      },
      {
        type: 'booking_ticket',
        data: {
          fields: ['name', 'email', 'phone', 'pickup_location', 'destination', 'date', 'time', 'car_type'],
          payment_methods: ['COD'],
        },
      },
    ],
    {
      image: 'https://placehold.co/200x120?text=Car+Booking',
      tags: ['car', 'transportation', 'taxi'],
      popularity: 70,
      theme: {
        primaryColor: '#ef4444',
        fontFamily: 'Inter, sans-serif',
        layout: LayoutType.VERTICAL,
      },
    },
  ),
]

/**
 * Get templates by category
 */
export function getTemplatesByCategory(category: TemplateCategory): BookingPageTemplate[] {
  return BOOKING_PAGE_TEMPLATES.filter(template => template.category === category)
}

/**
 * Get templates by tag
 */
export function getTemplatesByTag(tag: string): BookingPageTemplate[] {
  return BOOKING_PAGE_TEMPLATES.filter(template => template.tags.includes(tag))
}

/**
 * Get featured templates
 */
export function getFeaturedTemplates(): BookingPageTemplate[] {
  return BOOKING_PAGE_TEMPLATES.filter(template => template.isFeatured)
}

/**
 * Get new templates
 */
export function getNewTemplates(): BookingPageTemplate[] {
  return BOOKING_PAGE_TEMPLATES.filter(template => template.isNew)
}

/**
 * Get template by ID
 */
export function getTemplateById(id: string): BookingPageTemplate | undefined {
  return BOOKING_PAGE_TEMPLATES.find(template => template.id === id)
}

/**
 * Get template by template code
 */
export function getTemplateByCode(code: string): BookingPageTemplate | undefined {
  return BOOKING_PAGE_TEMPLATES.find(template => template.templateCode === code)
}
