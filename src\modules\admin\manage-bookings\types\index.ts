export type BookingStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'rejected'

export interface BookingItem {
  _id: string
  slug: string
  customerName: string
  customerEmail: string
  customerPhone: string
  bookingPageId: string
  bookingPageName?: string
  bookingDate: string
  bookingSlots: {
    date: string
    field: string
    fieldName: string
    time: string
    _id?: string
  }[]
  paymentMethod: string
  quantity: number
  status: BookingStatus
  createdAt: string
  notes?: string
}

export interface BookingFilters {
  searchQuery: string
  statusFilter: string
  bookingPageId?: string
  dateRange: {
    from: Date
    to: Date
  }
}

export interface PaginationState {
  currentPage: number
  pageSize: number
  total: number
}

export type ViewMode = 'day' | 'week' | 'month'

export interface ManualBookingData {
  customerName: string
  customerPhone: string
  bookingTime: string
  pageName: string
  notes?: string
}

export interface BookingListProps {
  bookings: BookingItem[]
  isLoading: boolean
  error: string | null
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  onRetry: () => void
  onAcceptAction: (id: string) => void
  onRejectAction: (id: string) => void
  onViewDetailsAction: (id: string) => void
  onCreateNew: () => void
  onClearFilters?: () => void
  hasFilters: boolean
}

export interface BookingCardProps {
  booking: BookingItem
  onAcceptAction: (id: string) => void
  onRejectAction: (id: string) => void
}

export interface BookingFiltersProps {
  searchQuery: string
  setSearchQuery: (query: string) => void
  statusFilter: string
  setStatusFilter: (status: string) => void
  bookingPageId?: string
  setBookingPageId?: (id: string) => void
  dateRange?: { from: Date, to: Date }
  setDateRange: (range: { from: Date, to: Date }) => void
  viewMode: ViewMode
  setViewMode: (mode: ViewMode) => void
}
