import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { bookingPageAPIs } from '@/modules/admin/apis/booking-page.api'
import { useCallback, useEffect, useState } from 'react'

/**
 * Custom hook to fetch and manage booking pages
 * This hook is separate from useBookings to avoid unnecessary API calls
 */
export const useBookingPages = () => {
  const [bookingPages, setBookingPages] = useState<BookingPageItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch booking pages from API
  const fetchBookingPages = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await bookingPageAPIs.getBookingPages()

      if (response && response.data) {
        const bookingPagesData = Array.isArray(response.data)
          ? response.data as BookingPageItem[]
          : [response.data as BookingPageItem]
        setBookingPages(bookingPagesData)
      } else {
        setBookingPages([])
      }
    } catch (err: any) {
      console.error('Error fetching booking pages:', err)
      setError(err.message || 'Failed to fetch booking pages')
      setBookingPages([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Fetch booking pages only once on mount
  useEffect(() => {
    fetchBookingPages()
  }, []) // Empty dependency array - only run once

  return {
    bookingPages,
    isLoading,
    error,
    refetch: fetchBookingPages,
  }
}

export default useBookingPages
