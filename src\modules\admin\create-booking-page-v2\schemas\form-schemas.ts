import { z } from 'zod'
import { RESERVED_SUBDOMAINS } from '../constants/validation'

// Page Info Schema
export const pageInfoSchema = z.object({
  name: z
    .string()
    .min(3, 'Tên trang phải có ít nhất 3 ký tự')
    .max(100, 'Tên trang không được quá 100 ký tự')
    .trim(),
  description: z
    .string()
    .trim(),
  slug: z
    .string()
    .min(3, 'Đường dẫn phải có ít nhất 3 ký tự')
    .max(50, 'Đường dẫn không được quá 50 ký tự')
    .regex(/^[a-z0-9-]+$/, 'Đường dẫn chỉ được chứa chữ cái thường, số và dấu gạch ngang')
    .refine(val => !val.startsWith('-') && !val.endsWith('-'), {
      message: 'Đường dẫn không được bắt đầu hoặc kết thúc bằng dấu gạch ngang',
    })
    .refine(val => !val.includes('--'), {
      message: 'Đường dẫn không được chứa hai dấu gạch ngang liên tiếp',
    })
    .refine(val => !RESERVED_SUBDOMAINS.includes(val.toLowerCase()), {
      message: 'Đường dẫn này đã được sử dụng, vui lòng chọn đường dẫn khác',
    }),
})

// Domain Config Schema
export const domainConfigSchema = z.object({
  subdomain: z
    .string()
    .min(3, 'Tên miền phụ phải có ít nhất 3 ký tự')
    .max(50, 'Tên miền phụ không được quá 50 ký tự')
    .regex(/^[a-z0-9-]+$/, 'Tên miền chỉ được chứa chữ cái thường, số và dấu gạch ngang')
    .refine(val => !val.startsWith('-') && !val.endsWith('-'), {
      message: 'Tên miền không được bắt đầu hoặc kết thúc bằng dấu gạch ngang',
    })
    .refine(val => !val.includes('--'), {
      message: 'Tên miền không được chứa hai dấu gạch ngang liên tiếp',
    })
    .refine(val => !RESERVED_SUBDOMAINS.includes(val.toLowerCase()), {
      message: 'Tên miền này đã được sử dụng, vui lòng chọn tên khác',
    }),
  customDomain: z
    .string()
    .url('URL không hợp lệ')
    .optional()
    .or(z.literal('')),
})

// Template Selection Schema
export const templateSelectionSchema = z.object({
  selectedTemplateId: z
    .string()
    .min(1, 'Vui lòng chọn một mẫu giao diện'),
})

// Booking Field Schema
export const bookingFieldSchema = z.object({
  id: z.string(),
  name: z
    .string()
    .min(2, 'Tên sân phải có ít nhất 2 ký tự')
    .max(50, 'Tên sân không được quá 50 ký tự')
    .trim(),
  type: z.enum(['football', 'tennis', 'badminton', 'basketball']),
  capacity: z
    .number()
    .min(1, 'Sức chứa phải ít nhất 1')
    .max(100, 'Sức chứa không được quá 100'),
})

// Booking Config Schema
export const bookingConfigSchema = z.object({
  bannerTitle: z
    .string()
    .min(5, 'Tiêu đề banner phải có ít nhất 5 ký tự')
    .max(100, 'Tiêu đề banner không được quá 100 ký tự')
    .trim(),
  bannerSubtitle: z
    .string()
    .max(200, 'Mô tả banner không được quá 200 ký tự')
    .trim()
    .optional()
    .or(z.literal('')),
  bannerImage: z
    .string()
    .url('URL hình ảnh không hợp lệ')
    .regex(/\.(jpg|jpeg|png|gif|webp)$/i, 'Hình ảnh phải có định dạng jpg, jpeg, png, gif hoặc webp')
    .optional()
    .or(z.literal('')),
  openTime: z
    .string()
    .regex(/^([01]?\d|2[0-3]):[0-5]\d$/, 'Thời gian không hợp lệ (HH:MM)'),
  closeTime: z
    .string()
    .regex(/^([01]?\d|2[0-3]):[0-5]\d$/, 'Thời gian không hợp lệ (HH:MM)'),
  fields: z
    .array(bookingFieldSchema)
    .min(1, 'Vui lòng thêm ít nhất một sân')
    .refine((fields) => {
      const names = fields.map(f => f.name.toLowerCase().trim())
      const uniqueNames = new Set(names)
      return names.length === uniqueNames.size
    }, {
      message: 'Tên sân không được trùng lặp',
    }),
}).refine((data) => {
  // Validate that close time is after open time
  const [openHours = 0, openMinutes = 0] = data.openTime.split(':').map(Number)
  const [closeHours = 0, closeMinutes = 0] = data.closeTime.split(':').map(Number)

  const openTotalMinutes = openHours * 60 + openMinutes
  const closeTotalMinutes = closeHours * 60 + closeMinutes

  return closeTotalMinutes > openTotalMinutes
}, {
  message: 'Giờ đóng cửa phải sau giờ mở cửa',
  path: ['closeTime'],
})

// Export form data types
export type PageInfoFormData = z.infer<typeof pageInfoSchema>
export type DomainConfigFormData = z.infer<typeof domainConfigSchema>
export type TemplateSelectionFormData = z.infer<typeof templateSelectionSchema>
export type BookingConfigFormData = z.infer<typeof bookingConfigSchema>
export type BookingFieldFormData = z.infer<typeof bookingFieldSchema>
