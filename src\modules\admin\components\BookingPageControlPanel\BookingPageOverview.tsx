'use client'

import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { PAGE_STATUSES, TEMPLATE_NAMES } from '@/modules/admin/constants/mock-data'
import { BarChart2, Calendar, Clock, Eye, Users } from 'lucide-react'
import Image from 'next/image'
import React from 'react'

interface BookingPageStats {
  totalViews: number
  totalBookings: number
  conversionRate: number
  averageBookingDuration: number
  popularTimes: string[]
  recentBookings: {
    id: string
    customerName: string
    time: string
    status: string
  }[]
}

interface BookingPageOverviewProps {
  bookingPage: BookingPageItem
  stats?: BookingPageStats | null
}

/**
 * Overview tab for the booking page control panel
 * Shows basic information and stats
 */
const BookingPageOverview: React.FC<BookingPageOverviewProps> = ({ bookingPage, stats: externalStats }) => {
  const {
    name,
    status,
    templateCode,
    createdAt,
    description,
  } = bookingPage

  // Lấy avatar từ bookingPage hoặc tạo avatar mặc định
  const avatar = (bookingPage as any).avatar || `https://placehold.co/100x100?text=${encodeURIComponent(name?.[0] || 'BP')}`

  // Lấy views và bookings từ stats hoặc từ bookingPage nếu có
  const views = externalStats?.totalViews || (bookingPage as any).views || 0
  const bookings = externalStats?.totalBookings || (bookingPage as any).bookings || 0

  const statusInfo = PAGE_STATUSES[status]
  const templateName = TEMPLATE_NAMES[templateCode as keyof typeof TEMPLATE_NAMES] || templateCode
  const createdDate = new Date(createdAt).toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })

  // Sử dụng stats từ props hoặc tạo stats mặc định
  const stats = externalStats || {
    totalBookings: bookings,
    totalViews: views,
    conversionRate: views > 0 ? Math.round((bookings / views) * 100) : 0,
    averageBookingDuration: 60, // minutes
    popularTimes: ['18:00 - 20:00', '08:00 - 10:00'],
    recentBookings: [
      { id: 'b1', customerName: 'Nguyễn Văn A', time: '2 giờ trước', status: 'confirmed' },
      { id: 'b2', customerName: 'Trần Thị B', time: '5 giờ trước', status: 'pending' },
      { id: 'b3', customerName: 'Lê Văn C', time: '1 ngày trước', status: 'confirmed' },
    ],
  }

  return (
    <div className="space-y-6">
      {/* Basic Info */}
      <div className="flex items-start gap-4">
        <div className="relative w-24 h-24 rounded-md overflow-hidden">
          <Image
            src={avatar}
            alt={name}
            fill
            className="object-cover"
          />
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold">{name}</h2>
          <p className="text-gray-600 mt-1">{description}</p>
          <div className="flex items-center gap-3 mt-2">
            <Badge
              variant="outline"
              className={`bg-${statusInfo.color}-50 text-${statusInfo.color}-700 border-${statusInfo.color}-200`}
            >
              {statusInfo.label}
            </Badge>
            <span className="text-sm text-gray-500">{templateName}</span>
            <span className="text-sm text-gray-500">
              Tạo ngày:
              {createdDate}
            </span>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Lượt đặt</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-primary mr-2" />
              <span className="text-2xl font-bold">{stats.totalBookings}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Lượt xem</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Eye className="h-5 w-5 text-primary mr-2" />
              <span className="text-2xl font-bold">{stats.totalViews}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Tỷ lệ chuyển đổi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <BarChart2 className="h-5 w-5 text-primary mr-2" />
              <span className="text-2xl font-bold">
                {stats.conversionRate}
                %
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Thời lượng TB</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-primary mr-2" />
              <span className="text-2xl font-bold">
                {stats.averageBookingDuration}
                {' '}
                phút
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Bookings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Đặt lịch gần đây</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats.recentBookings.map(booking => (
              <div key={booking.id} className="flex items-center justify-between border-b pb-3">
                <div className="flex items-center gap-3">
                  <div className="bg-gray-100 rounded-full p-2">
                    <Users className="h-4 w-4 text-gray-600" />
                  </div>
                  <div>
                    <div className="font-medium">{booking.customerName}</div>
                    <div className="text-sm text-gray-500">{booking.time}</div>
                  </div>
                </div>
                <Badge variant={booking.status === 'confirmed' ? 'default' : 'outline'}>
                  {booking.status === 'confirmed' ? 'Đã xác nhận' : 'Chờ xác nhận'}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default BookingPageOverview
