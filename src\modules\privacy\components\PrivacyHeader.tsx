'use client'

import { Button } from '@/components/ui/button'
import { useRouter } from '@/libs/i18nNavigation'
import { getToken } from '@/services/auth'
import { appPaths } from '@/utils/app-routes'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { FaHome, FaSignInAlt, FaUser, FaUserPlus } from 'react-icons/fa'

export const PrivacyHeader = () => {
  const router = useRouter()
  const headerRef = useRef<HTMLDivElement>(null)
  const logoRef = useRef<HTMLDivElement>(null)
  const navRef = useRef<HTMLDivElement>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isNavigating, setIsNavigating] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Check authentication status
  const checkAuth = useCallback(() => {
    const token = getToken()
    setIsAuthenticated(!!token)
    setIsLoading(false)
  }, [])

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  const handleNavigation = (path: string) => {
    router.push(path)
  }

  const handleDashboard = () => {
    setIsNavigating(true)
    router.push(appPaths.admin.dashboard())
  }

  if (isLoading) {
    return null // hoặc có thể return một loading spinner
  }

  return (
    <header
      ref={headerRef}
      className="bg-white shadow-sm py-4 sticky top-0 z-50"
    >
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div
            role="button"
            tabIndex={0}
            ref={logoRef}
            className="flex items-center cursor-pointer"
            onClick={() => handleNavigation('/')}
          >
            <div className="text-orange-500 text-2xl font-bold">PickSlot</div>
          </div>

          {/* Navigation */}
          <div ref={navRef} className="flex items-center space-x-2 md:space-x-4">
            <Button
              variant="ghost"
              className="flex items-center gap-1 md:gap-2 px-2 md:px-4"
              onClick={() => handleNavigation('/')}
            >
              <FaHome />
              <span className="hidden md:inline">Trang chủ</span>
            </Button>

            {isAuthenticated
              ? (
                  <Button
                    size="sm"
                    className="flex items-center gap-1 md:gap-2 px-2 md:px-4"
                    onClick={handleDashboard}
                  >
                    <FaUser className="w-4 h-4" />
                    <span className="hidden md:inline">{isNavigating ? 'Đang chuyển hướng...' : 'Quản lý'}</span>
                  </Button>
                )
              : (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="flex items-center gap-1 md:gap-2 px-2 md:px-4"
                      onClick={() => handleNavigation(appPaths.auth.login())}
                    >
                      <FaSignInAlt className="w-4 h-4" />
                      <span className="hidden md:inline">Đăng nhập</span>
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1 md:gap-2 border-orange-500 text-orange-500 hover:bg-orange-50 px-2 md:px-4"
                      onClick={() => handleNavigation(appPaths.auth.register())}
                    >
                      <FaUserPlus className="w-4 h-4" />
                      <span className="hidden md:inline">Đăng ký</span>
                    </Button>
                  </>
                )}
          </div>
        </div>
      </div>
    </header>
  )
}
