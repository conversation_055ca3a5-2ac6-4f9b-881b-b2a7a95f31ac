'use client'

import { SvgsAssets } from '@/assets/svgs'
import { Button } from '@/components/ui/button'
import { useNavigation } from '@/hooks/useNavigation'
import { getToken } from '@/services/auth'
import { appPaths } from '@/utils/app-routes'
import { motion } from 'framer-motion'
import React, { useCallback, useEffect, useState } from 'react'
import { FaSignInAlt, FaUser, FaUserPlus } from 'react-icons/fa'

export const HeaderNavigation = () => {
  const { navigate, isNavigating } = useNavigation()
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Check authentication status
  const checkAuth = useCallback(() => {
    const token = getToken()
    setIsAuthenticated(!!token)
  }, [])

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  const handleLogin = () => {
    navigate(appPaths.auth.login())
  }

  const handleRegister = () => {
    navigate(appPaths.auth.register())
  }

  const handleDashboard = () => {
    navigate(appPaths.admin.dashboard())
  }

  const handleHome = () => {
    navigate(appPaths.public.home())
  }

  // Animation variants
  const headerVariants = {
    hidden: { y: -100, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { y: -20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.4, ease: 'easeOut' },
    },
  }

  // Add loading overlay
  const loadingOverlay = isNavigating
    ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            className="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full"
          />
        </motion.div>
      )
    : null

  return (
    <>
      {loadingOverlay}
      <motion.header
        initial="hidden"
        animate="visible"
        variants={headerVariants}
        className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-100 shadow-sm"
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <motion.div
              variants={itemVariants}
              role="button"
              tabIndex={0}
              className="flex items-center gap-3 cursor-pointer hover:opacity-80 transition-opacity"
              onClick={handleHome}
            >
              <SvgsAssets.Logo width={32} height={32} />
              <span className="text-xl font-bold bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent">
                PickSlot
              </span>
            </motion.div>

            {/* Navigation */}
            <motion.div
              variants={itemVariants}
              className="flex items-center gap-2 md:gap-3"
            >
              {isAuthenticated
                ? (
                    <>
                      <Button
                        // variant="outline"
                        size="sm"
                        // className="nav-item flex items-center gap-1 md:gap-2 border-orange-500 text-orange-500 hover:bg-orange-50 px-2 md:px-3"
                        onClick={handleDashboard}
                      >
                        <FaUser className="w-4 h-4" />
                        <span className="hidden md:inline">{isNavigating ? 'Đang chuyển hướng...' : 'Quản lý'}</span>
                      </Button>
                    </>
                  )
                : (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="nav-item flex items-center gap-1 md:gap-2 text-gray-600 hover:text-orange-500 px-2 md:px-3"
                        onClick={handleLogin}
                      >
                        <FaSignInAlt className="w-4 h-4" />
                        <span className="hidden md:inline">Đăng nhập</span>
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="nav-item flex items-center gap-1 md:gap-2 border-orange-500 text-orange-500 hover:bg-orange-50 px-2 md:px-3"
                        onClick={handleRegister}
                      >
                        <FaUserPlus className="w-4 h-4" />
                        <span className="hidden md:inline">Đăng ký</span>
                      </Button>

                      <Button
                        size="sm"
                        className="nav-item bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-3 md:px-4"
                        onClick={handleRegister}
                      >
                        <span className="text-sm">Bắt đầu</span>
                      </Button>
                    </>
                  )}
            </motion.div>
          </div>
        </div>
      </motion.header>
    </>
  )
}
