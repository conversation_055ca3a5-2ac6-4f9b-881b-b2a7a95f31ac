export interface BookingPageBookingsProps {
  bookingPageId: string
}

export interface BookingSlotInfo {
  date: string
  fieldTimes: string
}

export interface FormattedBookingSlotsData {
  dates: string[]
  slotsInfo: BookingSlotInfo[] | string
}

export type BookingTabType = 'all' | 'pending' | 'confirmed' | 'cancelled'
export type DateFilterType = 'all' | 'today' | 'tomorrow' | 'thisWeek' | 'thisMonth'
export type StatusFilterType = 'all' | 'pending' | 'confirmed' | 'cancelled'

export interface BookingFilters {
  bookingTab: BookingTabType
  searchQuery: string
  statusFilter: StatusFilterType
  dateFilter: DateFilterType
}

export interface PaginationState {
  currentPage: number
  pageSize: number
  total: number
}

export interface PaginationControls {
  currentPage: number
  pageSize: number
  total: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  goToPage: (page: number) => void
  goToNextPage: () => void
  goToPreviousPage: () => void
  changePageSize: (size: number) => void
}
