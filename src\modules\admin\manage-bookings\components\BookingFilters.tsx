import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import type { BookingFiltersProps } from '../types'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DateRangePicker } from '@/modules/admin/components/DateRangePicker'
import { Search } from 'lucide-react'
import React from 'react'

const BOOKING_STATUSES = {
  pending: { label: 'Chờ xác nhận' },
  confirmed: { label: 'Đã xác nhận' },
  cancelled: { label: 'Đã hủy' },
  completed: { label: 'Hoàn thành' },
  rejected: { label: 'Từ chối' },
}

/**
 * Component for filtering bookings
 */
interface BookingFiltersComponentProps extends BookingFiltersProps {
  bookingPages?: BookingPageItem[]
  isLoadingBookingPages?: boolean
}

export const BookingFilters = ({
  searchQuery,
  setSearchQuery,
  statusFilter,
  setStatusFilter,
  bookingPageId,
  setBookingPageId,
  dateRange,
  setDateRange,
  bookingPages = [],
  isLoadingBookingPages = false,
}: BookingFiltersComponentProps) => {
  return (
    <div className="flex flex-wrap gap-4 mb-6">
      {/* Date Range Picker */}
      <div className="flex-1 min-w-[300px]">
        <DateRangePicker
          defaultDateRange={dateRange}
          onDateRangeChange={(range) => {
            if (range) {
              setDateRange(range)
            }
          }}
        />
      </div>

      {/* View Mode Selector */}
      {/* <ViewSelector viewMode={viewMode} onViewModeChange={setViewMode} /> */}

      {/* Status Filter */}
      <Select value={statusFilter} onValueChange={setStatusFilter}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Lọc theo trạng thái" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả trạng thái</SelectItem>
          {Object.entries(BOOKING_STATUSES).map(([key, { label }]) => (
            <SelectItem key={key} value={key}>{label}</SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Booking Page Filter */}
      {setBookingPageId && (
        <Select value={bookingPageId || 'all'} onValueChange={value => setBookingPageId(value === 'all' ? '' : value)}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder={isLoadingBookingPages ? 'Đang tải...' : 'Lọc theo trang đặt lịch'} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả trang</SelectItem>
            {isLoadingBookingPages
              ? (
                  <SelectItem value="loading" disabled>Đang tải...</SelectItem>
                )
              : (
                  bookingPages.map(page => (
                    <SelectItem key={page._id} value={page._id}>
                      {page.name}
                    </SelectItem>
                  ))
                )}
          </SelectContent>
        </Select>
      )}

      {/* Search Input */}
      <div className="relative flex-1 min-w-[300px]">
        <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <Input
          placeholder="Tìm kiếm theo tên, số điện thoại, email..."
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>
    </div>
  )
}

export default BookingFilters
