'use client'

import { Button } from '@/components/ui/button'
import { Check, ChevronLeft, ChevronRight } from 'lucide-react'
import React from 'react'
import { pageInfoSchema } from '../schemas/form-schemas'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

const TOTAL_STEPS = 3

export const NavigationFooter: React.FC = () => {
  const {
    currentStep,
    nextStep,
    prevStep,
    validateCurrentStep,
    pageInfo,
    selectedTemplateId,
    bookingConfig,
  } = useCreateBookingPageV2Store()

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return pageInfoSchema.safeParse(pageInfo)?.success
      case 2:
        return selectedTemplateId !== ''
      case 3:
        return (
          bookingConfig.bannerTitle.trim() !== ''
          && bookingConfig.openTime !== ''
          && bookingConfig.closeTime !== ''
          && bookingConfig.fields.length > 0
        )
      default:
        return false
    }
  }

  const handleNext = () => {
    if (validateCurrentStep()) {
      nextStep()
    }
  }

  const handleFinish = () => {
    if (validateCurrentStep()) {
      // TODO: Implement create booking page logic
    }
  }

  return (
    <div className="">
      <div className="bg-white/90 backdrop-blur-md border border-orange-200/50 rounded-2xl p-6 shadow-xl">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          {/* Back Button */}
          <Button
            onClick={prevStep}
            disabled={currentStep === 1}
            variant="outline"
            size="lg"
            className="w-full sm:w-auto border-orange-300 text-orange-600 hover:bg-orange-50 disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6"
          >
            <ChevronLeft className="w-5 h-5 mr-2" />
            Quay lại
          </Button>

          {/* Progress Dots */}
          <div className="flex items-center gap-3">
            {Array.from({ length: TOTAL_STEPS }, (_, index) => {
              const stepNumber = index + 1
              return (
                <div
                  key={stepNumber}
                  className={`rounded-full transition-all duration-300 ${
                    currentStep === stepNumber
                      ? 'bg-orange-500 w-10 h-3'
                      : currentStep > stepNumber
                        ? 'bg-green-500 w-3 h-3'
                        : 'bg-gray-300 w-3 h-3'
                  }`}
                />
              )
            })}
          </div>

          {/* Next/Finish Button */}
          {currentStep < TOTAL_STEPS
            ? (
                <Button
                  onClick={handleNext}
                  disabled={!canProceed()}
                  size="lg"
                  className="w-full sm:w-auto bg-orange-500 hover:bg-orange-600 text-white disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6 shadow-lg"
                >
                  Tiếp theo
                  <ChevronRight className="w-5 h-5 ml-2" />
                </Button>
              )
            : (
                <Button
                  onClick={handleFinish}
                  disabled={!canProceed()}
                  size="lg"
                  className="w-full sm:w-auto bg-green-500 hover:bg-green-600 text-white disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6 shadow-lg"
                >
                  <Check className="w-5 h-5 mr-2" />
                  Tạo trang đặt chỗ
                </Button>
              )}
        </div>
      </div>
    </div>
  )
}
