'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ModernSportTemplate, ClassicSportTemplate } from '../components/templates'
import type { BookingConfig, PageInfo } from '../types'

const TemplateDemo: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<'modern' | 'classic'>('modern')
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop')

  // Mock data for demo
  const mockPageInfo: PageInfo = {
    name: 'CLB Cầu lông B-ZONE 11',
    description: 'Sân cầu lông hiện đại với đầy đủ tiện nghi, phù hợp cho mọi lứa tuổi',
    slug: 'clb-cau-long-b-zone-11'
  }

  const mockBookingConfig: BookingConfig = {
    bannerTitle: 'CLB Cầu lông B-ZONE 11',
    bannerSubtitle: 'Đặt sân cầu lông chất lượng cao',
    bannerImage: '/api/placeholder/800/400',
    openTime: '06:00',
    closeTime: '22:00',
    fields: [
      {
        id: 'field-1',
        name: 'Sân 1',
        type: 'badminton',
        capacity: 4
      },
      {
        id: 'field-2',
        name: 'Sân 2',
        type: 'badminton',
        capacity: 4
      },
      {
        id: 'field-3',
        name: 'Sân 3',
        type: 'badminton',
        capacity: 4
      },
      {
        id: 'field-4',
        name: 'Sân VIP',
        type: 'badminton',
        capacity: 4
      }
    ]
  }

  const renderTemplate = () => {
    const commonProps = {
      config: mockBookingConfig,
      pageInfo: mockPageInfo,
      previewMode,
    }

    switch (selectedTemplate) {
      case 'modern':
        return <ModernSportTemplate {...commonProps} />
      case 'classic':
        return <ClassicSportTemplate {...commonProps} />
      default:
        return <ModernSportTemplate {...commonProps} />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Template Demo - Sân thể thao hiện đại
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Xem trước giao diện template cho trang đặt sân thể thao
          </p>
        </div>

        {/* Controls */}
        <div className="flex flex-wrap items-center justify-center gap-4 mb-8">
          {/* Template Selection */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">Template:</span>
            <Button
              variant={selectedTemplate === 'modern' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTemplate('modern')}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              Hiện đại
            </Button>
            <Button
              variant={selectedTemplate === 'classic' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTemplate('classic')}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              Cổ điển
            </Button>
          </div>

          {/* Preview Mode */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">Xem trước:</span>
            <Button
              variant={previewMode === 'desktop' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPreviewMode('desktop')}
            >
              Desktop
            </Button>
            <Button
              variant={previewMode === 'mobile' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPreviewMode('mobile')}
            >
              Mobile
            </Button>
          </div>

          {/* Template Info */}
          <Badge variant="secondary" className="bg-orange-100 text-orange-700">
            {selectedTemplate === 'modern' ? 'Sân thể thao hiện đại' : 'Sân thể thao cổ điển'}
          </Badge>
        </div>

        {/* Template Preview */}
        <Card className="border-orange-200">
          <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100 border-b border-orange-200">
            <CardTitle className="text-center">
              Xem trước template - {previewMode === 'desktop' ? 'Desktop' : 'Mobile'}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className={`bg-white rounded-lg border border-gray-200 overflow-hidden transition-all duration-300 ${
              previewMode === 'mobile' ? 'max-w-sm mx-auto' : 'w-full'
            }`}>
              {renderTemplate()}
            </div>
          </CardContent>
        </Card>

        {/* Template Features */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="border-orange-200">
            <CardHeader>
              <CardTitle className="text-orange-600">Template Hiện đại</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>✅ Layout 6 phần: Banner, Mô tả|Chọn ngày, Thông tin|Lịch đặt, Liên hệ|Bản đồ</li>
                <li>✅ Giao diện responsive, tối ưu mobile</li>
                <li>✅ Lịch đặt sân trực quan với grid slots</li>
                <li>✅ Tích hợp bản đồ và thông tin liên hệ</li>
                <li>✅ Màu sắc orange chủ đạo, hiện đại</li>
                <li>✅ Hỗ trợ nhiều loại sân thể thao</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="border-blue-200">
            <CardHeader>
              <CardTitle className="text-blue-600">Template Cổ điển</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>✅ Layout dọc truyền thống, dễ sử dụng</li>
                <li>✅ Giao diện đơn giản, phù hợp mọi lứa tuổi</li>
                <li>✅ Thông tin rõ ràng, dễ đọc</li>
                <li>✅ Màu sắc blue nhẹ nhàng</li>
                <li>✅ Tương thích tốt với mọi thiết bị</li>
                <li>✅ Tập trung vào nội dung chính</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default TemplateDemo
