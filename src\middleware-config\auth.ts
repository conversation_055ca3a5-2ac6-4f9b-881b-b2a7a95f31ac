import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { CookieUtils, LocaleUtils, RouteUtils, TokenValidator } from './utils'

// Admin authentication handler
export class AdminAuthHandler {
  static async handleAdminRoute(request: NextRequest): Promise<NextResponse | null> {
    const path = request.nextUrl.pathname

    if (!RouteUtils.isAdminRoute(path)) {
      return null // Not an admin route, continue processing
    }

    const token = CookieUtils.getToken(request)
    const { locale } = LocaleUtils.extractLocaleFromPath(path)
    const loginUrl = LocaleUtils.createLoginUrl(request, locale)

    // No token found
    if (!token) {
      return NextResponse.redirect(loginUrl)
    }

    // Validate token
    const validation = await TokenValidator.validateToken(token)
    if (!validation.isValid) {
      console.warn('Token invalid or expired, redirecting to login')

      const response = NextResponse.redirect(loginUrl)

      if (validation.shouldClearCookies) {
        CookieUtils.clearAuthCookies(response)
      }

      return response
    }

    return null // Token is valid, continue processing
  }
}
