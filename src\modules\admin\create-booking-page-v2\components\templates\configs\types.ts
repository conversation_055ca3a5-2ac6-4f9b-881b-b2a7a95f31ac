import type { BookingConfig, PageInfo } from '../../../types'

// Base Configuration Props
export interface BaseConfigProps {
  config: BookingConfig
  pageInfo: PageInfo
  onConfigChange: (updates: Partial<BookingConfig>) => void
  onPageInfoChange: (updates: Partial<PageInfo>) => void
}

// Template-specific Configuration Types
export interface ModernSportConfigData {
  bannerSettings: {
    showOverlay: boolean
    overlayOpacity: number
    textPosition: 'center' | 'left' | 'right'
  }
  layoutSettings: {
    sectionSpacing: 'compact' | 'normal' | 'spacious'
    borderRadius: 'none' | 'small' | 'medium' | 'large'
    shadowLevel: 'none' | 'small' | 'medium' | 'large'
  }
  colorSettings: {
    primaryColor: string
    accentColor: string
    backgroundColor: string
  }
  featureSettings: {
    showPricing: boolean
    showCapacity: boolean
    showRatings: boolean
    showSocialLinks: boolean
    enableQuickBooking: boolean
  }
}

export interface ClassicSportConfigData {
  layoutSettings: {
    maxWidth: 'narrow' | 'medium' | 'wide'
    sectionSpacing: 'compact' | 'normal' | 'spacious'
  }
  colorSettings: {
    primaryColor: string
    backgroundColor: string
  }
  featureSettings: {
    showAdvancedFilters: boolean
    enableMultiSelect: boolean
    showDetailedInfo: boolean
  }
}

// Configuration Component Props - Simplified
export interface ModernSportConfigProps extends BaseConfigProps {
  // No template-specific config needed, just use base config
}

export interface ClassicSportConfigProps extends BaseConfigProps {
  // No template-specific config needed, just use base config
}
