'use client'

import React from 'react'
import { Layout, <PERSON>lette, Setting<PERSON> } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import type { ClassicSportConfigProps } from './types'

const ClassicSportConfig: React.FC<ClassicSportConfigProps> = ({
  config,
  pageInfo,
  templateConfig,
  onConfigChange,
  onPageInfoChange,
  onTemplateConfigChange,
}) => {
  const updateLayoutSettings = (updates: Partial<typeof templateConfig.layoutSettings>) => {
    onTemplateConfigChange({
      layoutSettings: { ...templateConfig.layoutSettings, ...updates }
    })
  }

  const updateColorSettings = (updates: Partial<typeof templateConfig.colorSettings>) => {
    onTemplateConfigChange({
      colorSettings: { ...templateConfig.colorSettings, ...updates }
    })
  }

  const updateFeatureSettings = (updates: Partial<typeof templateConfig.featureSettings>) => {
    onTemplateConfigChange({
      featureSettings: { ...templateConfig.featureSettings, ...updates }
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Settings className="w-6 h-6 text-blue-500" />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Cấu hình Sân thể thao cổ điển
          </h3>
          <p className="text-sm text-gray-600">
            Tùy chỉnh giao diện đơn giản, dễ sử dụng
          </p>
        </div>
      </div>

      <Tabs defaultValue="layout" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="layout" className="flex items-center gap-2">
            <Layout className="w-4 h-4" />
            Bố cục
          </TabsTrigger>
          <TabsTrigger value="colors" className="flex items-center gap-2">
            <Palette className="w-4 h-4" />
            Màu sắc
          </TabsTrigger>
          <TabsTrigger value="features" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Tính năng
          </TabsTrigger>
        </TabsList>

        {/* Layout Settings */}
        <TabsContent value="layout" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Cài đặt bố cục</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Độ rộng tối đa</Label>
                <Select
                  value={templateConfig.layoutSettings.maxWidth}
                  onValueChange={(value: 'narrow' | 'medium' | 'wide') =>
                    updateLayoutSettings({ maxWidth: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="narrow">Hẹp (800px)</SelectItem>
                    <SelectItem value="medium">Vừa (1200px)</SelectItem>
                    <SelectItem value="wide">Rộng (1400px)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Khoảng cách giữa các phần</Label>
                <Select
                  value={templateConfig.layoutSettings.sectionSpacing}
                  onValueChange={(value: 'compact' | 'normal' | 'spacious') =>
                    updateLayoutSettings({ sectionSpacing: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="compact">Gọn gàng</SelectItem>
                    <SelectItem value="normal">Bình thường</SelectItem>
                    <SelectItem value="spacious">Rộng rãi</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Color Settings */}
        <TabsContent value="colors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Cài đặt màu sắc</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Màu chính</Label>
                <div className="flex items-center gap-3">
                  <input
                    type="color"
                    value={templateConfig.colorSettings.primaryColor}
                    onChange={(e) => updateColorSettings({ primaryColor: e.target.value })}
                    className="w-12 h-10 rounded border border-gray-300"
                  />
                  <span className="text-sm text-gray-600">
                    {templateConfig.colorSettings.primaryColor}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Màu nền</Label>
                <div className="flex items-center gap-3">
                  <input
                    type="color"
                    value={templateConfig.colorSettings.backgroundColor}
                    onChange={(e) => updateColorSettings({ backgroundColor: e.target.value })}
                    className="w-12 h-10 rounded border border-gray-300"
                  />
                  <span className="text-sm text-gray-600">
                    {templateConfig.colorSettings.backgroundColor}
                  </span>
                </div>
              </div>

              {/* Color Presets */}
              <div className="space-y-2">
                <Label>Bộ màu có sẵn</Label>
                <div className="grid grid-cols-4 gap-2">
                  {[
                    { name: 'Blue', primary: '#3b82f6', bg: '#eff6ff' },
                    { name: 'Green', primary: '#10b981', bg: '#ecfdf5' },
                    { name: 'Purple', primary: '#8b5cf6', bg: '#f5f3ff' },
                    { name: 'Gray', primary: '#6b7280', bg: '#f9fafb' },
                  ].map((preset) => (
                    <button
                      key={preset.name}
                      onClick={() => updateColorSettings({
                        primaryColor: preset.primary,
                        backgroundColor: preset.bg,
                      })}
                      className="p-2 rounded border border-gray-200 hover:border-gray-300 transition-colors"
                    >
                      <div className="flex gap-1">
                        <div className="w-6 h-6 rounded" style={{ backgroundColor: preset.primary }} />
                        <div className="w-6 h-6 rounded border" style={{ backgroundColor: preset.bg }} />
                      </div>
                      <div className="text-xs mt-1">{preset.name}</div>
                    </button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Feature Settings */}
        <TabsContent value="features" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Tính năng</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { key: 'showAdvancedFilters', label: 'Bộ lọc nâng cao', desc: 'Hiển thị các tùy chọn lọc chi tiết' },
                { key: 'enableMultiSelect', label: 'Chọn nhiều sân', desc: 'Cho phép đặt nhiều sân cùng lúc' },
                { key: 'showDetailedInfo', label: 'Thông tin chi tiết', desc: 'Hiển thị mô tả đầy đủ về sân' },
              ].map((feature) => (
                <div key={feature.key} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div>
                    <div className="font-medium text-gray-900">{feature.label}</div>
                    <div className="text-sm text-gray-600">{feature.desc}</div>
                  </div>
                  <Switch
                    checked={templateConfig.featureSettings[feature.key as keyof typeof templateConfig.featureSettings] as boolean}
                    onCheckedChange={(checked) => updateFeatureSettings({ [feature.key]: checked })}
                  />
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ClassicSportConfig
