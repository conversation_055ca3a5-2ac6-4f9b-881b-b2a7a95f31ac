# Create Booking Page V2

Phiên bản cải tiến của trang tạo booking page với architecture tốt hơn, sử dụng Zustand và component-based design.

## 📁 Cấu trúc thư mục

```
create-booking-page-v2/
├── components/           # UI Components
│   ├── steps/           # Step-specific components
│   │   ├── DomainConfigStep.tsx
│   │   ├── TemplateSelectionStep.tsx
│   │   └── BookingConfigStep.tsx
│   ├── ProgressHeader.tsx
│   ├── NavigationFooter.tsx
│   └── index.ts
├── constants/           # Static data và configuration
│   ├── templates.ts
│   ├── validation.ts
│   └── index.ts
├── hooks/              # Custom hooks
│   ├── useStepValidation.ts
│   ├── useCreateBookingPage.ts
│   └── index.ts
├── stores/             # Zustand stores
│   └── create-booking-page-v2.store.ts
├── types/              # TypeScript types
│   └── index.ts
├── utils/              # Utility functions
│   ├── validation.ts
│   ├── helpers.ts
│   └── index.ts
├── screens/            # Main screen components
│   └── CreateBookingPageV2Screen.tsx
├── index.ts            # Main exports
└── README.md
```

## 🎯 Tính năng chính

### **3 Bước tạo booking page:**

1. **Domain Configuration** - Thiết lập tên miền
2. **Template Selection** - Chọn mẫu giao diện
3. **Booking Configuration** - Cấu hình chi tiết

### **State Management với Zustand:**
- Centralized state management
- Không cần prop drilling
- DevTools support
- Type-safe

### **Validation System:**
- Real-time validation
- Step-by-step validation
- Custom validation rules
- Error handling

## 🚀 Cách sử dụng

### Import và sử dụng:

```tsx
import { CreateBookingPageV2Screen } from '@/modules/admin/create-booking-page-v2'

export default function Page() {
  return <CreateBookingPageV2Screen />
}
```

### Sử dụng store:

```tsx
import { useCreateBookingPageV2Store } from '@/modules/admin/create-booking-page-v2'

function MyComponent() {
  const {
    currentStep,
    domainConfig,
    updateDomainConfig,
    nextStep,
    prevStep
  } = useCreateBookingPageV2Store()
  
  // Component logic
}
```

### Sử dụng hooks:

```tsx
import { useStepValidation, useCreateBookingPage } from '@/modules/admin/create-booking-page-v2'

function MyComponent() {
  const { validateCurrentStep, getFieldError } = useStepValidation()
  const { createBookingPage, isCreating } = useCreateBookingPage()
  
  // Component logic
}
```

## 📋 API Reference

### Store Actions

- `setCurrentStep(step: number)` - Chuyển đến bước cụ thể
- `nextStep()` - Chuyển đến bước tiếp theo
- `prevStep()` - Quay lại bước trước
- `updateDomainConfig(config)` - Cập nhật cấu hình domain
- `setSelectedTemplate(id)` - Chọn template
- `updateBookingConfig(config)` - Cập nhật cấu hình booking
- `addField()` - Thêm sân mới
- `removeField(id)` - Xóa sân
- `updateField(id, updates)` - Cập nhật thông tin sân
- `validateCurrentStep()` - Validate bước hiện tại
- `reset()` - Reset toàn bộ state

### Validation Rules

- **Domain**: Required, pattern matching, reserved names
- **Template**: Required selection
- **Booking Config**: Required fields, time validation, field validation

## 🎨 UI Components

### Step Components
- `DomainConfigStep` - Form cấu hình domain
- `TemplateSelectionStep` - Grid chọn template với preview
- `BookingConfigStep` - Form cấu hình booking chi tiết

### Layout Components
- `ProgressHeader` - Header với progress indicator
- `NavigationFooter` - Footer với navigation buttons

## 🔧 Customization

### Thêm template mới:

```tsx
// constants/templates.ts
export const TEMPLATES: TemplateConfig[] = [
  // existing templates...
  {
    id: 'new-template',
    name: 'Template mới',
    category: TemplateCategory.SPORT,
    preview: '/path/to/preview.jpg',
    description: 'Mô tả template',
    features: ['Feature 1', 'Feature 2']
  }
]
```

### Thêm validation rule:

```tsx
// constants/validation.ts
export const STEP_VALIDATIONS: StepValidation[] = [
  {
    step: 1,
    rules: [
      {
        field: 'newField',
        required: true,
        custom: (value) => {
          // Custom validation logic
          return true
        }
      }
    ]
  }
]
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage
```

## 📝 Notes

- Sử dụng TypeScript cho type safety
- Responsive design cho mọi device
- Accessibility support
- Error handling comprehensive
- Performance optimized với React.memo và useCallback
