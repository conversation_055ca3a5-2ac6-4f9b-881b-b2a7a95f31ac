'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Eye, Monitor, Settings, Smartphone } from 'lucide-react'
import React, { useState } from 'react'
import { TEMPLATES } from '../../constants/templates'
import { useCreateBookingPageV2Store } from '../../stores/create-booking-page-v2.store'
import { ClassicSportTemplate, ModernSportTemplate } from '../templates'
import { ClassicSportConfig, ModernSportConfig } from '../templates/configs'

export const BookingConfigStep: React.FC = () => {
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop')

  const {
    pageInfo,
    selectedTemplateId,
    bookingConfig,
    updateBookingConfig,
  } = useCreateBookingPageV2Store()

  // Get selected template
  const selectedTemplate = TEMPLATES.find(t => t.id === selectedTemplateId)

  // Render template preview
  const renderTemplatePreview = () => {
    const commonProps = {
      config: bookingConfig,
      pageInfo,
      previewMode,
    }

    switch (selectedTemplateId) {
      case 'sport-modern':
        return <ModernSportTemplate {...commonProps} />
      case 'sport-classic':
        return <ClassicSportTemplate {...commonProps} />
      default:
        return (
          <div className="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
            <p className="text-gray-500">Chưa chọn template</p>
          </div>
        )
    }
  }

  // Render template configuration
  const renderTemplateConfig = () => {
    const commonProps = {
      config: bookingConfig,
      pageInfo,
      onConfigChange: updateBookingConfig,
      onPageInfoChange: () => {}, // Will be handled by parent
    }

    switch (selectedTemplateId) {
      case 'sport-modern':
        return <ModernSportConfig {...commonProps} />
      case 'sport-classic':
        return <ClassicSportConfig {...commonProps} />
      default:
        return (
          <div className="text-center py-8">
            <p className="text-gray-500">Vui lòng chọn template để cấu hình</p>
          </div>
        )
    }
  }

  return (
    <div className="animate-in fade-in slide-in-from-right-4 duration-300">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 flex items-center justify-center gap-3 mb-4">
            <Settings className="w-7 h-7 lg:w-8 lg:h-8 text-orange-500" />
            Cấu hình trang đặt chỗ
          </h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
            Thiết lập thông tin và xem trước giao diện trang đặt chỗ của bạn
          </p>
          {selectedTemplate && (
            <div className="mt-4 inline-flex items-center gap-2 px-4 py-2 bg-orange-100 text-orange-700 rounded-full text-sm font-medium">
              <Eye className="w-4 h-4" />
              Template:
              {' '}
              {selectedTemplate.name}
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Configuration Panel */}
          <div className="space-y-6">
            <Card className="border-orange-200">
              <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100 border-b border-orange-200">
                <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-3">
                  <Settings className="w-5 h-5 text-orange-500" />
                  Cấu hình template
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {renderTemplateConfig()}
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="space-y-6">
            <Card className="border-orange-200">
              <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100 border-b border-orange-200">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-3">
                    <Eye className="w-5 h-5 text-orange-500" />
                    Xem trước
                  </CardTitle>

                  {/* Preview Mode Toggle */}
                  <div className="flex items-center gap-2 bg-white rounded-lg p-1 border border-gray-200">
                    <Button
                      type="button"
                      variant={previewMode === 'desktop' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setPreviewMode('desktop')}
                      className="h-8 w-8 p-0"
                    >
                      <Monitor className="w-4 h-4" />
                    </Button>
                    <Button
                      type="button"
                      variant={previewMode === 'mobile' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setPreviewMode('mobile')}
                      className="h-8 w-8 p-0"
                    >
                      <Smartphone className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="p-6">
                <div className={`bg-gray-50 rounded-xl p-4 max-h-[600px] overflow-y-auto transition-all duration-300 ${
                  previewMode === 'mobile' ? 'max-w-sm mx-auto' : 'w-full'
                }`}
                >
                  {renderTemplatePreview()}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingConfigStep
