import type { NextRequest } from 'next/server'
import arcjet from '@/libs/Arcjet'
import { detectBot } from '@arcjet/next'

// Security service
export class SecurityService {
  private static readonly arcjet = arcjet.withRule(
    detectBot({
      mode: 'LIVE',
      allow: [
        'CATEGORY:SEARCH_ENGINE',
        'CATEGORY:PREVIEW',
        'CATEGORY:MONITOR',
      ],
    }),
  )

  static async checkSecurity(request: NextRequest): Promise<void> {
    if (!process.env.ARCJET_KEY) {
      return
    }

    try {
      const decision = await this.arcjet.protect(request)

      if (decision.isDenied()) {
        if (decision.reason.isBot()) {
          throw new Error('No bots allowed')
        }
        throw new Error('Access denied')
      }
    } catch (err) {
    // Nếu quota hết hoặc lỗi Arcjet server → bỏ qua
      console.warn('Arcjet error or quota exceeded, skipping security check:', err)
    }
  }
}
