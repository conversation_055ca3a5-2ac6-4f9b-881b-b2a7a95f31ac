'use client'

import React, { useState } from 'react'
import { Calendar, Clock, MapPin, Star } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar as CalendarComponent } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import type { DescriptionSectionProps } from './types'

const DescriptionSection: React.FC<DescriptionSectionProps> = ({
  config,
  pageInfo,
  previewMode = 'desktop',
  className,
  description,
  openTime,
  closeTime,
  location,
}) => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [isCalendarOpen, setIsCalendarOpen] = useState(false)
  const isMobile = previewMode === 'mobile'

  return (
    <div className={cn(
      'bg-white rounded-lg border border-gray-200 p-6',
      className
    )}>
      <div className={cn(
        'grid gap-6',
        isMobile ? 'grid-cols-1' : 'grid-cols-2'
      )}>
        {/* Description Column */}
        <div className="space-y-4">
          <div>
            <h2 className={cn(
              'font-bold text-gray-900 mb-3',
              isMobile ? 'text-lg' : 'text-xl'
            )}>
              Thông tin sân
            </h2>
            <p className={cn(
              'text-gray-600 leading-relaxed',
              isMobile ? 'text-sm' : 'text-base'
            )}>
              {description || pageInfo.description || 'Sân thể thao hiện đại với đầy đủ tiện nghi, phù hợp cho mọi lứa tuổi. Chúng tôi cam kết mang đến trải nghiệm tốt nhất cho khách hàng.'}
            </p>
          </div>

          {/* Info Items */}
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Clock className="w-5 h-5 text-orange-500 flex-shrink-0" />
              <span className={cn(
                'text-gray-700',
                isMobile ? 'text-sm' : 'text-base'
              )}>
                Giờ hoạt động: {openTime || config.openTime || '06:00'} - {closeTime || config.closeTime || '22:00'}
              </span>
            </div>
            
            <div className="flex items-center gap-3">
              <MapPin className="w-5 h-5 text-orange-500 flex-shrink-0" />
              <span className={cn(
                'text-gray-700',
                isMobile ? 'text-sm' : 'text-base'
              )}>
                {location || 'Sân thể thao ABC, Quận 1, TP.HCM'}
              </span>
            </div>

            <div className="flex items-center gap-3">
              <Star className="w-5 h-5 text-orange-500 flex-shrink-0" />
              <span className={cn(
                'text-gray-700',
                isMobile ? 'text-sm' : 'text-base'
              )}>
                Đánh giá: 4.8/5 (128 đánh giá)
              </span>
            </div>
          </div>
        </div>

        {/* Date Selection Column */}
        <div className="space-y-4">
          <h3 className={cn(
            'font-bold text-gray-900',
            isMobile ? 'text-lg' : 'text-xl'
          )}>
            Chọn ngày đặt sân
          </h3>
          
          <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'w-full justify-start text-left font-normal border-orange-200 hover:bg-orange-50',
                  !selectedDate && 'text-muted-foreground',
                  isMobile ? 'h-10 text-sm' : 'h-12 text-base'
                )}
              >
                <Calendar className="mr-2 h-4 w-4 text-orange-500" />
                {selectedDate ? (
                  format(selectedDate, 'EEEE, dd/MM/yyyy', { locale: vi })
                ) : (
                  <span>Chọn ngày</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                mode="single"
                selected={selectedDate}
                onSelect={(date) => {
                  setSelectedDate(date || new Date())
                  setIsCalendarOpen(false)
                }}
                disabled={(date) => date < new Date()}
                initialFocus
                locale={vi}
              />
            </PopoverContent>
          </Popover>

          {/* Quick Date Selection */}
          <div className="grid grid-cols-2 gap-2">
            {['Hôm nay', 'Ngày mai', 'Thứ 7', 'Chủ nhật'].map((label, index) => {
              const date = new Date()
              date.setDate(date.getDate() + index)
              
              return (
                <Button
                  key={label}
                  variant="outline"
                  size="sm"
                  className="border-orange-200 text-orange-600 hover:bg-orange-50"
                  onClick={() => setSelectedDate(date)}
                >
                  {label}
                </Button>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DescriptionSection
