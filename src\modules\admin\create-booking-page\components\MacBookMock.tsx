import { Env } from '@/libs/Env'
import React from 'react'
import { useBookingPageConfigStore } from '../stores/booking-page-config'

type MacBookMockProps = {
  children: React.ReactNode
}

const MacBookMock: React.FC<MacBookMockProps> = ({ children }) => {
  const { pageInfo } = useBookingPageConfigStore()

  return (
    <div className="macbook-container mx-auto shadow-2xl relative">
      {/* MacBook Frame */}
      <div className="macbook-frame bg-gray-800 rounded-t-lg pt-4 pb-1 px-2">
        <div className="flex justify-center items-center">
          <div className="w-2 h-2 rounded-full bg-gray-600 mr-1"></div>
          <div className="w-16 h-1.5 rounded-full bg-gray-700"></div>
        </div>
      </div>

      {/* Screen - Tỉ lệ 16:10 */}
      <div className="macbook-screen bg-white border-x-8 border-t-8 border-gray-800 ">
        <div className="macbook-content h-full ">
          {/* Browser Chrome */}
          <div className="browser-chrome bg-gray-100 border-b border-gray-300 py-1 px-2 flex items-center">
            <div className="flex space-x-1.5 mr-4">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
            </div>
            <div className="flex-1 mx-2">
              <div className="bg-white rounded-full h-6 flex items-center px-3 text-xs text-gray-500 overflow-hidden">
                <span className="truncate">
                  {Env.NEXT_PUBLIC_DOMAIN}
                  /
                  {pageInfo?.slug}
                </span>
              </div>
            </div>
          </div>

          {/* Content - Tỉ lệ 16:10 */}
          <div
            className="browser-content pt-0 p-2 bg-gray-50 overflow-auto"
          >
            {children}
          </div>
        </div>
      </div>

      {/* Base */}
      <div className="macbook-base bg-gray-800 h-3 rounded-b-xl relative">
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/4 h-1 bg-gray-700 rounded-t-sm"></div>
      </div>

      {/* Reflection */}
      <div className="macbook-reflection w-full h-4 bg-gradient-to-b from-gray-300 to-transparent opacity-30 rounded-b-full"></div>
    </div>
  )
}

export default MacBookMock
